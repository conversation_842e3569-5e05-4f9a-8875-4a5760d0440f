using TorchSharp;
using TorchSharp.Modules;
using static TorchSharp.torch;
using static TorchSharp.torch.nn;

namespace FloorPlanGAN.Models
{
    /// <summary>
    /// 条件生成器 - 根据外轮廓生成建筑平面图
    /// 基于DCGAN架构，参考教程实现
    /// </summary>
    public class ConditionalGenerator : Module<Tensor, Tensor>
    {
        private readonly FloorPlanGANOptions _options;
        private readonly Sequential _main;

        public ConditionalGenerator(FloorPlanGANOptions options) : base(nameof(ConditionalGenerator))
        {
            _options = options;

            // 基于DCGAN的生成器架构，但输入是条件（外轮廓）+ 噪声
            _main = Sequential(
                // 输入: 条件特征 + 噪声，输出到卷积
                ("conv_transpose1", ConvTranspose2d(options.NoiseSize + options.Channels, options.GeneratorFeatures * 8, 4, 1, 0, bias: false)),
                ("bn1", BatchNorm2d(options.GeneratorFeatures * 8)),
                ("relu1", ReLU(true)),

                // state size: (ngf*8) x 4 x 4
                ("conv_transpose2", ConvTranspose2d(options.GeneratorFeatures * 8, options.GeneratorFeatures * 4, 4, 2, 1, bias: false)),
                ("bn2", BatchNorm2d(options.GeneratorFeatures * 4)),
                ("relu2", ReLU(true)),

                // state size: (ngf*4) x 8 x 8
                ("conv_transpose3", ConvTranspose2d(options.GeneratorFeatures * 4, options.GeneratorFeatures * 2, 4, 2, 1, bias: false)),
                ("bn3", BatchNorm2d(options.GeneratorFeatures * 2)),
                ("relu3", ReLU(true)),

                // state size: (ngf*2) x 16 x 16
                ("conv_transpose4", ConvTranspose2d(options.GeneratorFeatures * 2, options.GeneratorFeatures, 4, 2, 1, bias: false)),
                ("bn4", BatchNorm2d(options.GeneratorFeatures)),
                ("relu4", ReLU(true)),

                // state size: (ngf) x 32 x 32
                ("conv_transpose5", ConvTranspose2d(options.GeneratorFeatures, options.Channels, 4, 2, 1, bias: false)),
                ("tanh", Tanh())
                // state size: (nc) x 64 x 64
            );

            RegisterComponents();
        }

        public override Tensor forward(Tensor input)
        {
            return _main.forward(input);
        }

        /// <summary>
        /// 初始化权重 - 基于DCGAN论文的建议
        /// </summary>
        public void InitializeWeights()
        {
            this.apply(WeightsInit);
        }

        /// <summary>
        /// 权重初始化函数
        /// </summary>
        private static void WeightsInit(Module m)
        {
            var classname = m.GetType().Name;
            if (classname.Contains("Conv"))
            {
                if (m is Conv2d conv2d)
                {
                    init.normal_(conv2d.weight, 0.0, 0.02);
                }
                else if (m is ConvTranspose2d convTranspose2d)
                {
                    init.normal_(convTranspose2d.weight, 0.0, 0.02);
                }
            }
            else if (classname.Contains("BatchNorm"))
            {
                if (m is BatchNorm2d batchNorm2d)
                {
                    init.normal_(batchNorm2d.weight, 1.0, 0.02);
                    init.zeros_(batchNorm2d.bias);
                }
            }
        }
    }
}
