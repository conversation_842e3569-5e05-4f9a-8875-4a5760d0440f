{"version": 3, "targets": {"net8.0": {"Google.Protobuf/3.21.9": {"type": "package", "compile": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "libtorch-cpu/*******": {"type": "package", "dependencies": {"libtorch-cpu-linux-x64": "*******", "libtorch-cpu-osx-x64": "*******", "libtorch-cpu-win-x64": "*******"}, "build": {"buildTransitive/netstandard2.0/libtorch-cpu.props": {}, "buildTransitive/netstandard2.0/libtorch-cpu.targets": {}}}, "libtorch-cpu-linux-x64/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cpu-linux-x64.props": {}, "buildTransitive/netstandard2.0/libtorch-cpu-linux-x64.targets": {}}, "runtimeTargets": {"runtimes/linux-x64/native/libbackend_with_compiler.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libc10.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libfbjni.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libgomp-a34b3233.so.1": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libjitbackend_test.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libnnapi_backend.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libpytorch_jni.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libshm.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libtorch.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libtorch_cpu.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libtorch_global_deps.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libtorch_python.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libtorchbind_test.so": {"assetType": "native", "rid": "linux-x64"}}}, "libtorch-cpu-osx-x64/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cpu-osx-x64.props": {}, "buildTransitive/netstandard2.0/libtorch-cpu-osx-x64.targets": {}}, "runtimeTargets": {"runtimes/osx-x64/native/libbackend_with_compiler.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libc10.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libfbjni.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libiomp5.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libjitbackend_test.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libpytorch_jni.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libshm.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libtorch.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libtorch_cpu.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libtorch_global_deps.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libtorch_python.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libtorchbind_test.dylib": {"assetType": "native", "rid": "osx-x64"}}}, "libtorch-cpu-win-x64/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cpu-win-x64.props": {}, "buildTransitive/netstandard2.0/libtorch-cpu-win-x64.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/asmjit.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/c10.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/fbgemm.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/fbjni.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/libiomp5md.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/libiompstubs5md.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/pytorch_jni.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/torch.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/torch_cpu.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/torch_global_deps.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/uv.dll": {"assetType": "native", "rid": "win-x64"}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "SharpZipLib/1.4.0": {"type": "package", "compile": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.ImageSharp/3.1.11": {"type": "package", "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "build": {"build/SixLabors.ImageSharp.props": {}}}, "SkiaSharp/2.88.6": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.6", "SkiaSharp.NativeAssets.macOS": "2.88.6"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.Drawing.Common/8.0.8": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "compile": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "TorchSharp/0.101.5": {"type": "package", "dependencies": {"Google.Protobuf": "3.21.9", "SharpZipLib": "1.4.0", "SkiaSharp": "2.88.6", "System.Memory": "4.5.5"}, "compile": {"lib/net6.0/TorchSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/TorchSharp.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/TorchSharp.props": {}, "buildTransitive/net6.0/TorchSharp.targets": {}}, "runtimeTargets": {"runtimes/linux-x64/native/libLibTorchSharp.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-x64/native/libLibTorchSharp.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/LibTorchSharp.dll": {"assetType": "native", "rid": "win-x64"}}}, "TorchSharp-cpu/0.101.5": {"type": "package", "dependencies": {"TorchSharp": "0.101.5", "libtorch-cpu": "*******"}, "build": {"buildTransitive/net6.0/TorchSharp-cpu.props": {}, "buildTransitive/net6.0/TorchSharp-cpu.targets": {}}}}}, "libraries": {"Google.Protobuf/3.21.9": {"sha512": "OTpFujTgkmqMLbg3KT7F/iuKi1rg6s5FCS2M9XcVLDn40zL8wgXm37CY/F6MeOEXKjdcnXGCN/h7oyMkVydVsg==", "type": "package", "path": "google.protobuf/3.21.9", "files": [".nupkg.metadata", ".signature.p7s", "google.protobuf.3.21.9.nupkg.sha512", "google.protobuf.nuspec", "lib/net45/Google.Protobuf.dll", "lib/net45/Google.Protobuf.pdb", "lib/net45/Google.Protobuf.xml", "lib/net5.0/Google.Protobuf.dll", "lib/net5.0/Google.Protobuf.pdb", "lib/net5.0/Google.Protobuf.xml", "lib/netstandard1.1/Google.Protobuf.dll", "lib/netstandard1.1/Google.Protobuf.pdb", "lib/netstandard1.1/Google.Protobuf.xml", "lib/netstandard2.0/Google.Protobuf.dll", "lib/netstandard2.0/Google.Protobuf.pdb", "lib/netstandard2.0/Google.Protobuf.xml"]}, "libtorch-cpu/*******": {"sha512": "SzckGCxIXSJQ2l2f7NaO3gFzNrgrxmYz248inHPZ3z7qMLqiKvTNCRVDNlMiKwLsI5x3wRzpsOwXrZT1d+SXgA==", "type": "package", "path": "libtorch-cpu/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cpu.props", "buildTransitive/netstandard2.0/libtorch-cpu.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cpu.*******.nupkg.sha512", "libtorch-cpu.nuspec"]}, "libtorch-cpu-linux-x64/*******": {"sha512": "gUdsUJMKdRaP0rkwYaaUhfP/Ue/hWM40qhWyhnkUJSk0Wc+BqQRoN/YlEm2PVyPvYTGQsAuaYR8v9K0uOxCWiA==", "type": "package", "path": "libtorch-cpu-linux-x64/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cpu-linux-x64.props", "buildTransitive/netstandard2.0/libtorch-cpu-linux-x64.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cpu-linux-x64.*******.nupkg.sha512", "libtorch-cpu-linux-x64.nuspec", "runtimes/linux-x64/native/libbackend_with_compiler.so", "runtimes/linux-x64/native/libc10.so", "runtimes/linux-x64/native/libfbjni.so", "runtimes/linux-x64/native/libgomp-a34b3233.so.1", "runtimes/linux-x64/native/libjitbackend_test.so", "runtimes/linux-x64/native/libnnapi_backend.so", "runtimes/linux-x64/native/libpytorch_jni.so", "runtimes/linux-x64/native/libshm.so", "runtimes/linux-x64/native/libtorch.so", "runtimes/linux-x64/native/libtorch_cpu.so", "runtimes/linux-x64/native/libtorch_global_deps.so", "runtimes/linux-x64/native/libtorch_python.so", "runtimes/linux-x64/native/libtorchbind_test.so"]}, "libtorch-cpu-osx-x64/*******": {"sha512": "LpgbGL8hLhunAQHtDvfLlcbAPmKxxyHl9LqXMSKpe/LhJdfBJa1uJxIHsb00cVTef53nUnP1X3/I6MHhAwSZHw==", "type": "package", "path": "libtorch-cpu-osx-x64/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cpu-osx-x64.props", "buildTransitive/netstandard2.0/libtorch-cpu-osx-x64.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cpu-osx-x64.*******.nupkg.sha512", "libtorch-cpu-osx-x64.nuspec", "runtimes/osx-x64/native/libbackend_with_compiler.dylib", "runtimes/osx-x64/native/libc10.dylib", "runtimes/osx-x64/native/libfbjni.dylib", "runtimes/osx-x64/native/libiomp5.dylib", "runtimes/osx-x64/native/libjitbackend_test.dylib", "runtimes/osx-x64/native/libpytorch_jni.dylib", "runtimes/osx-x64/native/libshm.dylib", "runtimes/osx-x64/native/libtorch.dylib", "runtimes/osx-x64/native/libtorch_cpu.dylib", "runtimes/osx-x64/native/libtorch_global_deps.dylib", "runtimes/osx-x64/native/libtorch_python.dylib", "runtimes/osx-x64/native/libtorchbind_test.dylib"]}, "libtorch-cpu-win-x64/*******": {"sha512": "zh9Ll+DefpXfMzaaEzHgtY3Q9iDi6NHTnETI0Jah5jGiPrzphTidsVxLC+njAeeThhFGRakx4CMwPqJFMvfw7A==", "type": "package", "path": "libtorch-cpu-win-x64/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cpu-win-x64.props", "buildTransitive/netstandard2.0/libtorch-cpu-win-x64.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cpu-win-x64.*******.nupkg.sha512", "libtorch-cpu-win-x64.nuspec", "runtimes/win-x64/native/asmjit.dll", "runtimes/win-x64/native/c10.dll", "runtimes/win-x64/native/fbgemm.dll", "runtimes/win-x64/native/fbjni.dll", "runtimes/win-x64/native/libiomp5md.dll", "runtimes/win-x64/native/libiompstubs5md.dll", "runtimes/win-x64/native/pytorch_jni.dll", "runtimes/win-x64/native/torch.dll", "runtimes/win-x64/native/torch_cpu.dll", "runtimes/win-x64/native/torch_global_deps.dll", "runtimes/win-x64/native/uv.dll"]}, "Microsoft.Win32.SystemEvents/8.0.0": {"sha512": "9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "type": "package", "path": "microsoft.win32.systemevents/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.8.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "SharpZipLib/1.4.0": {"sha512": "CdkbBSPIpHD8xBlu+8kDJiqc1Tf9iV89BObnqcvEbwysXSj5h1MfaeLgeeaxPZmi7CTJO8FDofBBNxBW0Vml7A==", "type": "package", "path": "sharpziplib/1.4.0", "files": [".nupkg.metadata", ".signature.p7s", "images/sharpziplib-nuget-256x256.png", "lib/net6.0/ICSharpCode.SharpZipLib.dll", "lib/net6.0/ICSharpCode.SharpZipLib.pdb", "lib/net6.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.0/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.0/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.1/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.1/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.1/ICSharpCode.SharpZipLib.xml", "sharpziplib.1.4.0.nupkg.sha512", "sharpziplib.nuspec"]}, "SixLabors.ImageSharp/3.1.11": {"sha512": "JfPLyigLthuE50yi6tMt7Amrenr/fA31t2CvJyhy/kQmfulIBAqo5T/YFUSRHtuYPXRSaUHygFeh6Qd933EoSw==", "type": "package", "path": "sixlabors.imagesharp/3.1.11", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "build/SixLabors.ImageSharp.props", "lib/net6.0/SixLabors.ImageSharp.dll", "lib/net6.0/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.3.1.11.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "SkiaSharp/2.88.6": {"sha512": "wdfeBAQrEQCbJIRgAiargzP1Uy+0grZiG4CSgBnhAgcJTsPzlifIaO73JRdwIlT3TyBoeU9jEqzwFUhl4hTYnQ==", "type": "package", "path": "skiasharp/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.6.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"sha512": "Sko9LFxRXSjb3OGh5/RxrVRXxYo48tr5NKuuSy6jB85GrYt8WRqVY1iLOLwtjPiVAt4cp+pyD4i30azanS64dw==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.6.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"sha512": "7TzFO0u/g2MpQsTty4fyCDdMcfcWI+aLswwfnYXr3gtNS6VLKdMXPMeKpJa3pJSLnUBN6wD0JjuCe8OoLBQ6cQ==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.6.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "System.Drawing.Common/8.0.8": {"sha512": "4ZM1wvLjz9nVVExsfPAaSl/qOvU+QNedJL5rQ+2Wbow+iGeyO0e7XN07707rMBgaffEeeLrCZBwC0oHUuvRdPw==", "type": "package", "path": "system.drawing.common/8.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.pdb", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.pdb", "lib/net7.0/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.8.0.8.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "TorchSharp/0.101.5": {"sha512": "iKZyVgf3vJpVlU4rH94zr2K8mAhFslO2+htHCMoBRU089sjGnLarLvJT2KFDOOahsB0SZEsPFkScsAsj3iqj/A==", "type": "package", "path": "torchsharp/0.101.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "buildTransitive/net6.0/TorchSharp.props", "buildTransitive/net6.0/TorchSharp.targets", "lib/net6.0/TorchSharp.dll", "lib/net6.0/TorchSharp.xml", "lib/netstandard2.0/TorchSharp.dll", "lib/netstandard2.0/TorchSharp.xml", "runtimes/linux-x64/native/libLibTorchSharp.so", "runtimes/osx-x64/native/libLibTorchSharp.dylib", "runtimes/win-x64/native/LibTorchSharp.dll", "torchsharp.0.101.5.nupkg.sha512", "torchsharp.nuspec"]}, "TorchSharp-cpu/0.101.5": {"sha512": "p1+pYO1MyrC6LT5suhu/SpserikhcP+JM9ic49+iNsU+LWW9y+UNZcjr4Nldy4g6Uz63qhFImINfWBQ1PXUBjw==", "type": "package", "path": "torchsharp-cpu/0.101.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "LICENSE.txt", "buildTransitive/net6.0/TorchSharp-cpu.props", "buildTransitive/net6.0/TorchSharp-cpu.targets", "buildTransitive/netstandard2.0/TorchSharp-cpu.props", "buildTransitive/netstandard2.0/TorchSharp-cpu.targets", "lib/net6.0/_._/empty.txt", "lib/netstandard2.0/_._/empty.txt", "torchsharp-cpu.0.101.5.nupkg.sha512", "torchsharp-cpu.nuspec"]}}, "projectFileDependencyGroups": {"net8.0": ["SixLabors.ImageSharp >= 3.1.11", "System.Drawing.Common >= 8.0.8", "TorchSharp >= 0.101.5", "TorchSharp-cpu >= 0.101.5"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj", "projectName": "FloorPlanGAN", "projectPath": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.11, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.8, )"}, "TorchSharp": {"target": "Package", "version": "[0.101.5, )"}, "TorchSharp-cpu": {"target": "Package", "version": "[0.101.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}