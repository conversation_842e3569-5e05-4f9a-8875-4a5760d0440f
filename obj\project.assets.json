{"version": 3, "targets": {"net8.0": {"Google.Protobuf/3.21.9": {"type": "package", "compile": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "libtorch-cuda-12.1-win-x64/*******": {"type": "package", "dependencies": {"libtorch-cuda-12.1-win-x64-part1": "*******", "libtorch-cuda-12.1-win-x64-part10": "*******", "libtorch-cuda-12.1-win-x64-part2": "*******", "libtorch-cuda-12.1-win-x64-part3-fragment1": "*******", "libtorch-cuda-12.1-win-x64-part3-primary": "*******", "libtorch-cuda-12.1-win-x64-part4": "*******", "libtorch-cuda-12.1-win-x64-part5-fragment1": "*******", "libtorch-cuda-12.1-win-x64-part5-primary": "*******", "libtorch-cuda-12.1-win-x64-part6": "*******", "libtorch-cuda-12.1-win-x64-part7": "*******", "libtorch-cuda-12.1-win-x64-part8": "*******", "libtorch-cuda-12.1-win-x64-part9-fragment1": "*******", "libtorch-cuda-12.1-win-x64-part9-fragment2": "*******", "libtorch-cuda-12.1-win-x64-part9-primary": "*******"}, "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64.targets": {}}}, "libtorch-cuda-12.1-win-x64-part1/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part1.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part1.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/asmjit.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/c10.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/c10_cuda.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/caffe2_nvrtc.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cublas64_12.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cudart64_12.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cudnn64_8.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/nvJitLink_120_0.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/nvrtc64_120_0.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/torch_global_deps.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/uv.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/zlibwapi.dll": {"assetType": "native", "rid": "win-x64"}}}, "libtorch-cuda-12.1-win-x64-part10/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part10.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part10.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/cufft64_11.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cufftw64_11.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cupti64_2023.1.1.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/curand64_10.dll": {"assetType": "native", "rid": "win-x64"}}}, "libtorch-cuda-12.1-win-x64-part2/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part2.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part2.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/cudnn_adv_infer64_8.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cudnn_adv_train64_8.dll": {"assetType": "native", "rid": "win-x64"}}}, "libtorch-cuda-12.1-win-x64-part3-fragment1/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part3-fragment1.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part3-fragment1.targets": {}}}, "libtorch-cuda-12.1-win-x64-part3-primary/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part3-primary.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part3-primary.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/cudnn_cnn_infer64_8.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cudnn_cnn_infer64_8.dll.sha": {"assetType": "native", "rid": "win-x64"}}}, "libtorch-cuda-12.1-win-x64-part4/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part4.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part4.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/cudnn_cnn_train64_8.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cudnn_ops_infer64_8.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cudnn_ops_train64_8.dll": {"assetType": "native", "rid": "win-x64"}}}, "libtorch-cuda-12.1-win-x64-part5-fragment1/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part5-fragment1.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part5-fragment1.targets": {}}}, "libtorch-cuda-12.1-win-x64-part5-primary/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part5-primary.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part5-primary.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/cublasLt64_12.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cublasLt64_12.dll.sha": {"assetType": "native", "rid": "win-x64"}}}, "libtorch-cuda-12.1-win-x64-part6/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part6.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part6.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/cusolver64_11.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/cusolverMg64_11.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/fbgemm.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/fbjni.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/libiomp5md.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/libiompstubs5md.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/nvToolsExt64_1.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/nvrtc-builtins64_121.dll": {"assetType": "native", "rid": "win-x64"}}}, "libtorch-cuda-12.1-win-x64-part7/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part7.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part7.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/pytorch_jni.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/torch.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/torch_cpu.dll": {"assetType": "native", "rid": "win-x64"}}}, "libtorch-cuda-12.1-win-x64-part8/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part8.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part8.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/cusparse64_12.dll": {"assetType": "native", "rid": "win-x64"}}}, "libtorch-cuda-12.1-win-x64-part9-fragment1/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-fragment1.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-fragment1.targets": {}}}, "libtorch-cuda-12.1-win-x64-part9-fragment2/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-fragment2.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-fragment2.targets": {}}}, "libtorch-cuda-12.1-win-x64-part9-primary/*******": {"type": "package", "build": {"buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-primary.props": {}, "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-primary.targets": {}}, "runtimeTargets": {"runtimes/win-x64/native/torch_cuda.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/torch_cuda.dll.sha": {"assetType": "native", "rid": "win-x64"}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "SharpZipLib/1.4.0": {"type": "package", "compile": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.ImageSharp/3.1.11": {"type": "package", "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "build": {"build/SixLabors.ImageSharp.props": {}}}, "SkiaSharp/2.88.6": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.6", "SkiaSharp.NativeAssets.macOS": "2.88.6"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.Drawing.Common/8.0.8": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "compile": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "TorchSharp/0.102.7": {"type": "package", "dependencies": {"Google.Protobuf": "3.21.9", "SharpZipLib": "1.4.0", "SkiaSharp": "2.88.6", "System.Memory": "4.5.5"}, "compile": {"lib/net6.0/TorchSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/TorchSharp.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/TorchSharp.props": {}, "buildTransitive/net6.0/TorchSharp.targets": {}}, "runtimeTargets": {"runtimes/linux-x64/native/libLibTorchSharp.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/libLibTorchSharp.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libLibTorchSharp.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/LibTorchSharp.dll": {"assetType": "native", "rid": "win-x64"}}}, "TorchSharp-cuda-windows/0.102.7": {"type": "package", "dependencies": {"TorchSharp": "0.102.7", "libtorch-cuda-12.1-win-x64": "*******"}, "build": {"buildTransitive/net6.0/TorchSharp-cuda-windows.props": {}, "buildTransitive/net6.0/TorchSharp-cuda-windows.targets": {}}}}}, "libraries": {"Google.Protobuf/3.21.9": {"sha512": "OTpFujTgkmqMLbg3KT7F/iuKi1rg6s5FCS2M9XcVLDn40zL8wgXm37CY/F6MeOEXKjdcnXGCN/h7oyMkVydVsg==", "type": "package", "path": "google.protobuf/3.21.9", "files": [".nupkg.metadata", ".signature.p7s", "google.protobuf.3.21.9.nupkg.sha512", "google.protobuf.nuspec", "lib/net45/Google.Protobuf.dll", "lib/net45/Google.Protobuf.pdb", "lib/net45/Google.Protobuf.xml", "lib/net5.0/Google.Protobuf.dll", "lib/net5.0/Google.Protobuf.pdb", "lib/net5.0/Google.Protobuf.xml", "lib/netstandard1.1/Google.Protobuf.dll", "lib/netstandard1.1/Google.Protobuf.pdb", "lib/netstandard1.1/Google.Protobuf.xml", "lib/netstandard2.0/Google.Protobuf.dll", "lib/netstandard2.0/Google.Protobuf.pdb", "lib/netstandard2.0/Google.Protobuf.xml"]}, "libtorch-cuda-12.1-win-x64/*******": {"sha512": "fSFsCwBtQqoLV5Ede1fsymQHU1Fw50JN5A+29hu+IHuTOskFLM7AUUFTm8ljlyVghlC8CjsuYhjFSQoyt8PGXQ==", "type": "package", "path": "libtorch-cuda-12.1-win-x64/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64.nuspec"]}, "libtorch-cuda-12.1-win-x64-part1/*******": {"sha512": "FJuypx3MUPbbMWDqj0vxAcQs7Xs9GK2D1ed1BeqN10lt7MpsTyXUORg8pxokzbbjEBlnf0wC8d25JN1oL46l4w==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part1/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part1.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part1.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part1.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part1.nuspec", "runtimes/win-x64/native/asmjit.dll", "runtimes/win-x64/native/c10.dll", "runtimes/win-x64/native/c10_cuda.dll", "runtimes/win-x64/native/caffe2_nvrtc.dll", "runtimes/win-x64/native/cublas64_12.dll", "runtimes/win-x64/native/cudart64_12.dll", "runtimes/win-x64/native/cudnn64_8.dll", "runtimes/win-x64/native/nvJitLink_120_0.dll", "runtimes/win-x64/native/nvrtc64_120_0.dll", "runtimes/win-x64/native/torch_global_deps.dll", "runtimes/win-x64/native/uv.dll", "runtimes/win-x64/native/zlibwapi.dll"]}, "libtorch-cuda-12.1-win-x64-part10/*******": {"sha512": "Pl/4S1P5Axgs7VAsdIm+zXwyd238zsKqOHnL6alNGF6UmjW4scSfLvemzQ80iK08VSqUOo2Zf2PDRD4T2iAyoA==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part10/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part10.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part10.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part10.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part10.nuspec", "runtimes/win-x64/native/cufft64_11.dll", "runtimes/win-x64/native/cufftw64_11.dll", "runtimes/win-x64/native/cupti64_2023.1.1.dll", "runtimes/win-x64/native/curand64_10.dll"]}, "libtorch-cuda-12.1-win-x64-part2/*******": {"sha512": "OcnU8im9yaNVY7aaCLPw8olPNsjW/WEwR8h2ZY/qLgGsXBtGxzfan+2xewgt+JnBclmaNdCZjKTNlQl+53ZxFQ==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part2/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part2.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part2.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part2.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part2.nuspec", "runtimes/win-x64/native/cudnn_adv_infer64_8.dll", "runtimes/win-x64/native/cudnn_adv_train64_8.dll"]}, "libtorch-cuda-12.1-win-x64-part3-fragment1/*******": {"sha512": "AJbZauBUa/H40PdfMRPURtgAK/UAXoC15zsYW0Rh1tN6U5LRny/LzU+B9rOCHFajHwgU/E/WjVqWJhybR5dumw==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part3-fragment1/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part3-fragment1.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part3-fragment1.targets", "fragments/win-x64/native/cudnn_cnn_infer64_8.dll.fragment1", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part3-fragment1.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part3-fragment1.nuspec"]}, "libtorch-cuda-12.1-win-x64-part3-primary/*******": {"sha512": "FZFdU5C1CgfJXy/Z07aVJzxF0so3c6MBLiibP4x+6GbXUt/gdZAQPfAQDs3U5eLX0R7OcIVBZcz+ib4QjoA+cA==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part3-primary/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part3-primary.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part3-primary.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part3-primary.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part3-primary.nuspec", "runtimes/win-x64/native/cudnn_cnn_infer64_8.dll", "runtimes/win-x64/native/cudnn_cnn_infer64_8.dll.sha"]}, "libtorch-cuda-12.1-win-x64-part4/*******": {"sha512": "AvVKciGKmsKGXD+hEYe+nrBSjWdGT1XIILEHAPswyJtVscER1aoz+CySYxhkaWIWrsI354W7nUZwxcCiKu3dWQ==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part4/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part4.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part4.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part4.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part4.nuspec", "runtimes/win-x64/native/cudnn_cnn_train64_8.dll", "runtimes/win-x64/native/cudnn_ops_infer64_8.dll", "runtimes/win-x64/native/cudnn_ops_train64_8.dll"]}, "libtorch-cuda-12.1-win-x64-part5-fragment1/*******": {"sha512": "x6MF6VfuqXiPhfAKld91fSnaX3V/p9MWTIvMbmzyC7MIpF+uCvKMCuVEhgC0aeIlFEkPCNq7KAY9zxYPrWVLEw==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part5-fragment1/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part5-fragment1.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part5-fragment1.targets", "fragments/win-x64/native/cublasLt64_12.dll.fragment1", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part5-fragment1.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part5-fragment1.nuspec"]}, "libtorch-cuda-12.1-win-x64-part5-primary/*******": {"sha512": "qdOSsyO6F+XLhS6OMd/oMeQc0s41NUKeluMelyX5+bj6btOnQy/KshWdUqDI+KpjY2zG/FOwNKBMNjf3/KFT5Q==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part5-primary/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part5-primary.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part5-primary.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part5-primary.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part5-primary.nuspec", "runtimes/win-x64/native/cublasLt64_12.dll", "runtimes/win-x64/native/cublasLt64_12.dll.sha"]}, "libtorch-cuda-12.1-win-x64-part6/*******": {"sha512": "08Bsxlwp2I7K0oKgwDUC+Y6M55Nge8Pa7z9/gvRHrM5nv4stSWUN3V7t8WVeZfiSqj0iXeLNlP3eynt6qBiRzg==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part6/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part6.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part6.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part6.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part6.nuspec", "runtimes/win-x64/native/cusolver64_11.dll", "runtimes/win-x64/native/cusolverMg64_11.dll", "runtimes/win-x64/native/fbgemm.dll", "runtimes/win-x64/native/fbjni.dll", "runtimes/win-x64/native/libiomp5md.dll", "runtimes/win-x64/native/libiompstubs5md.dll", "runtimes/win-x64/native/nvToolsExt64_1.dll", "runtimes/win-x64/native/nvrtc-builtins64_121.dll"]}, "libtorch-cuda-12.1-win-x64-part7/*******": {"sha512": "vBLDVpx5wgCoqSV6ncYTFzJkVwr5/iWJC+y1HliLy5270iOD/lQ2WHNBbqy8YciaRYRYLKOQEha/yMJG542b4g==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part7/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part7.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part7.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part7.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part7.nuspec", "runtimes/win-x64/native/pytorch_jni.dll", "runtimes/win-x64/native/torch.dll", "runtimes/win-x64/native/torch_cpu.dll"]}, "libtorch-cuda-12.1-win-x64-part8/*******": {"sha512": "WTS+eU2qW4B3mkW8dUwisj1wBzg0krMD0fV+Ryq9a6eBqsT2y/NKTL4+567unvwxDaAqJBfC7nLNgVBwLcNgGA==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part8/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part8.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part8.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part8.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part8.nuspec", "runtimes/win-x64/native/cusparse64_12.dll"]}, "libtorch-cuda-12.1-win-x64-part9-fragment1/*******": {"sha512": "ij3t/I+kkVrBFSuB1/S3lNKblHeiLLlyhrgoRwu2m8mAPEJoioHO5H/4sSM0UDp3YRYp31EAlK1e+pkHsCTZiw==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part9-fragment1/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-fragment1.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-fragment1.targets", "fragments/win-x64/native/torch_cuda.dll.fragment1", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part9-fragment1.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part9-fragment1.nuspec"]}, "libtorch-cuda-12.1-win-x64-part9-fragment2/*******": {"sha512": "cOqLuqdig1+Ra6REXB44Hmou7xL2ZYESxPrTS0MFocwUz1h31Q4YVw4a0YUQpWf/rx8vh26Gb18jRW02/wUlxA==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part9-fragment2/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-fragment2.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-fragment2.targets", "fragments/win-x64/native/torch_cuda.dll.fragment2", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part9-fragment2.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part9-fragment2.nuspec"]}, "libtorch-cuda-12.1-win-x64-part9-primary/*******": {"sha512": "cgI/G0BZeAoXmYtzOqaSApCDse5iYFk5WPqCcq8vwLV3R2Ag5D5eMKz5eKgjD5d0etJmJzloyYFixXp2rHpC7Q==", "type": "package", "path": "libtorch-cuda-12.1-win-x64-part9-primary/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-primary.props", "buildTransitive/netstandard2.0/libtorch-cuda-12.1-win-x64-part9-primary.targets", "lib/netstandard2.0/_._/empty.txt", "libtorch-cuda-12.1-win-x64-part9-primary.*******.nupkg.sha512", "libtorch-cuda-12.1-win-x64-part9-primary.nuspec", "runtimes/win-x64/native/torch_cuda.dll", "runtimes/win-x64/native/torch_cuda.dll.sha"]}, "Microsoft.Win32.SystemEvents/8.0.0": {"sha512": "9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "type": "package", "path": "microsoft.win32.systemevents/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.8.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "SharpZipLib/1.4.0": {"sha512": "CdkbBSPIpHD8xBlu+8kDJiqc1Tf9iV89BObnqcvEbwysXSj5h1MfaeLgeeaxPZmi7CTJO8FDofBBNxBW0Vml7A==", "type": "package", "path": "sharpziplib/1.4.0", "files": [".nupkg.metadata", ".signature.p7s", "images/sharpziplib-nuget-256x256.png", "lib/net6.0/ICSharpCode.SharpZipLib.dll", "lib/net6.0/ICSharpCode.SharpZipLib.pdb", "lib/net6.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.0/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.0/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.1/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.1/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.1/ICSharpCode.SharpZipLib.xml", "sharpziplib.1.4.0.nupkg.sha512", "sharpziplib.nuspec"]}, "SixLabors.ImageSharp/3.1.11": {"sha512": "JfPLyigLthuE50yi6tMt7Amrenr/fA31t2CvJyhy/kQmfulIBAqo5T/YFUSRHtuYPXRSaUHygFeh6Qd933EoSw==", "type": "package", "path": "sixlabors.imagesharp/3.1.11", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "build/SixLabors.ImageSharp.props", "lib/net6.0/SixLabors.ImageSharp.dll", "lib/net6.0/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.3.1.11.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "SkiaSharp/2.88.6": {"sha512": "wdfeBAQrEQCbJIRgAiargzP1Uy+0grZiG4CSgBnhAgcJTsPzlifIaO73JRdwIlT3TyBoeU9jEqzwFUhl4hTYnQ==", "type": "package", "path": "skiasharp/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.6.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"sha512": "Sko9LFxRXSjb3OGh5/RxrVRXxYo48tr5NKuuSy6jB85GrYt8WRqVY1iLOLwtjPiVAt4cp+pyD4i30azanS64dw==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.6.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"sha512": "7TzFO0u/g2MpQsTty4fyCDdMcfcWI+aLswwfnYXr3gtNS6VLKdMXPMeKpJa3pJSLnUBN6wD0JjuCe8OoLBQ6cQ==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.6.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "System.Drawing.Common/8.0.8": {"sha512": "4ZM1wvLjz9nVVExsfPAaSl/qOvU+QNedJL5rQ+2Wbow+iGeyO0e7XN07707rMBgaffEeeLrCZBwC0oHUuvRdPw==", "type": "package", "path": "system.drawing.common/8.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.pdb", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.pdb", "lib/net7.0/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.8.0.8.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "TorchSharp/0.102.7": {"sha512": "P4bl1QcZxD+ZLrPjblUSv6MjRLlDWKa6loPZMMfekIubiandR2gdloL4cQZmfblxf5JzaUWwsbcoBqMu1uHZCw==", "type": "package", "path": "torchsharp/0.102.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "buildTransitive/net6.0/TorchSharp.props", "buildTransitive/net6.0/TorchSharp.targets", "lib/net6.0/TorchSharp.dll", "lib/net6.0/TorchSharp.xml", "lib/netstandard2.0/TorchSharp.dll", "lib/netstandard2.0/TorchSharp.xml", "runtimes/linux-x64/native/libLibTorchSharp.so", "runtimes/osx-arm64/native/libLibTorchSharp.dylib", "runtimes/osx-x64/native/libLibTorchSharp.dylib", "runtimes/win-x64/native/LibTorchSharp.dll", "torchsharp.0.102.7.nupkg.sha512", "torchsharp.nuspec"]}, "TorchSharp-cuda-windows/0.102.7": {"sha512": "7bDTN3mIL0OPBq9fx+TUE4p2ta7Dx7ExrXpNKHMKEFzx1N1165bAmzAPORiQmWsQduDMFb+DsMHJ7AWfVL88wQ==", "type": "package", "path": "torchsharp-cuda-windows/0.102.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LIBTORCH.txt", "LICENSE.txt", "buildTransitive/net6.0/TorchSharp-cuda-windows.props", "buildTransitive/net6.0/TorchSharp-cuda-windows.targets", "buildTransitive/netstandard2.0/TorchSharp-cuda-windows.props", "buildTransitive/netstandard2.0/TorchSharp-cuda-windows.targets", "lib/net6.0/_._/empty.txt", "lib/netstandard2.0/_._/empty.txt", "torchsharp-cuda-windows.0.102.7.nupkg.sha512", "torchsharp-cuda-windows.nuspec"]}}, "projectFileDependencyGroups": {"net8.0": ["SixLabors.ImageSharp >= 3.1.11", "System.Drawing.Common >= 8.0.8", "TorchSharp >= 0.102.7", "TorchSharp-cuda-windows >= 0.102.7"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj", "projectName": "FloorPlanGAN", "projectPath": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.11, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.8, )"}, "TorchSharp": {"target": "Package", "version": "[0.102.7, )"}, "TorchSharp-cuda-windows": {"target": "Package", "version": "[0.102.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}