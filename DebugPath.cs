using System;
using System.IO;

namespace FloorPlanGAN
{
    public class DebugPath
    {
        public static void TestPaths()
        {
            Console.WriteLine("=== 路径调试信息 ===");

            var dataRoot = "Data/Floorplans/DataRoot";
            Console.WriteLine($"数据根目录: {dataRoot}");
            Console.WriteLine($"当前工作目录: {Directory.GetCurrentDirectory()}");

            // 测试绝对路径
            var absoluteDataRoot = Path.GetFullPath(dataRoot);
            Console.WriteLine($"绝对路径: {absoluteDataRoot}");
            Console.WriteLine($"数据根目录存在: {Directory.Exists(dataRoot)}");
            Console.WriteLine($"绝对路径存在: {Directory.Exists(absoluteDataRoot)}");

            // 测试子目录
            var outlinesDir = Path.Combine(dataRoot, "outlines");
            var floorplansDir = Path.Combine(dataRoot, "floorplans");

            Console.WriteLine($"外轮廓目录: {outlinesDir}");
            Console.WriteLine($"外轮廓目录存在: {Directory.Exists(outlinesDir)}");
            Console.WriteLine($"平面图目录: {floorplansDir}");
            Console.WriteLine($"平面图目录存在: {Directory.Exists(floorplansDir)}");

            // 测试 FloorPlanGANOptions 的默认路径
            var options = new FloorPlanGAN.Models.FloorPlanGANOptions();
            Console.WriteLine($"FloorPlanGANOptions.DataRoot: {options.DataRoot}");
            Console.WriteLine($"FloorPlanGANOptions.DataRoot 存在: {Directory.Exists(options.DataRoot)}");

            // 列出目录内容
            if (Directory.Exists(dataRoot))
            {
                Console.WriteLine($"数据根目录内容:");
                foreach (var dir in Directory.GetDirectories(dataRoot))
                {
                    Console.WriteLine($"  目录: {dir}");
                }
                foreach (var file in Directory.GetFiles(dataRoot))
                {
                    Console.WriteLine($"  文件: {file}");
                }
            }
            else
            {
                Console.WriteLine($"❌ 数据根目录不存在: {dataRoot}");
            }

            if (Directory.Exists(outlinesDir))
            {
                Console.WriteLine($"外轮廓目录内容:");
                foreach (var file in Directory.GetFiles(outlinesDir))
                {
                    Console.WriteLine($"  文件: {file}");
                }
            }
            else
            {
                Console.WriteLine($"❌ 外轮廓目录不存在: {outlinesDir}");
            }

            if (Directory.Exists(floorplansDir))
            {
                Console.WriteLine($"平面图目录内容:");
                foreach (var file in Directory.GetFiles(floorplansDir))
                {
                    Console.WriteLine($"  文件: {file}");
                }
            }
            else
            {
                Console.WriteLine($"❌ 平面图目录不存在: {floorplansDir}");
            }
        }
    }
}
