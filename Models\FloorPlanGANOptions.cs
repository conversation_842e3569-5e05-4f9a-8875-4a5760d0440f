using TorchSharp;

namespace FloorPlanGAN.Models
{
    /// <summary>
    /// 建筑平面图GAN训练配置
    /// </summary>
    public class FloorPlanGANOptions
    {
        /// <summary>
        /// 数据集根目录
        /// 结构应该是：
        /// DataRoot/
        /// ├── outlines/     # 外轮廓图片 (条件输入)
        /// │   ├── 001.png
        /// │   ├── 002.png
        /// │   └── ...
        /// └── floorplans/   # 对应的平面图 (目标输出)
        ///     ├── 001.png
        ///     ├── 002.png
        ///     └── ...
        /// </summary>
        public string DataRoot { get; set; } = "../../../Data/Floorplans/DataRoot";

        /// <summary>
        /// 数据加载器工作线程数
        /// </summary>
        public int Workers { get; set; } = 4;

        /// <summary>
        /// 训练批次大小
        /// </summary>
        public int BatchSize { get; set; } = 16;

        /// <summary>
        /// 图像尺寸 (正方形)
        /// 所有图像将被调整为此尺寸
        /// </summary>
        public int ImageSize { get; set; } = 256;

        /// <summary>
        /// 图像通道数 (RGB=3, 灰度=1)
        /// </summary>
        public int Channels { get; set; } = 3;

        /// <summary>
        /// 噪声向量维度
        /// </summary>
        public int NoiseSize { get; set; } = 100;

        /// <summary>
        /// 生成器特征图数量
        /// </summary>
        public int GeneratorFeatures { get; set; } = 64;

        /// <summary>
        /// 判别器特征图数量
        /// </summary>
        public int DiscriminatorFeatures { get; set; } = 64;

        /// <summary>
        /// 训练轮数
        /// </summary>
        public int NumEpochs { get; set; } = 200;

        /// <summary>
        /// 学习率
        /// </summary>
        public double LearningRate { get; set; } = 0.0002;

        /// <summary>
        /// Adam优化器Beta1参数
        /// </summary>
        public double Beta1 { get; set; } = 0.5;

        /// <summary>
        /// Adam优化器Beta2参数
        /// </summary>
        public double Beta2 { get; set; } = 0.999;

        /// <summary>
        /// 使用的设备 (CUDA/CPU)
        /// </summary>
        public torch.Device Device { get; set; } = torch.cuda.is_available() ? torch.CUDA : torch.CPU;

        /// <summary>
        /// 模型保存目录
        /// </summary>
        public string CheckpointDir { get; set; } = "checkpoints";

        /// <summary>
        /// 生成样本保存目录
        /// </summary>
        public string OutputDir { get; set; } = "generated_samples";

        /// <summary>
        /// 每多少个epoch保存一次模型
        /// </summary>
        public int SaveInterval { get; set; } = 10;

        /// <summary>
        /// 每多少个epoch生成一次样本
        /// </summary>
        public int SampleInterval { get; set; } = 5;

        /// <summary>
        /// 判别器训练次数 vs 生成器训练次数的比例
        /// </summary>
        public int DiscriminatorSteps { get; set; } = 1;

        /// <summary>
        /// 生成器训练次数
        /// </summary>
        public int GeneratorSteps { get; set; } = 1;

        /// <summary>
        /// 标签平滑参数 (真实标签的噪声)
        /// </summary>
        public double LabelSmoothing { get; set; } = 0.1;

        /// <summary>
        /// 随机种子
        /// </summary>
        public int RandomSeed { get; set; } = 42;

        /// <summary>
        /// 是否使用谱归一化
        /// </summary>
        public bool UseSpectralNorm { get; set; } = true;

        /// <summary>
        /// 是否使用自注意力机制
        /// </summary>
        public bool UseSelfAttention { get; set; } = false;

        /// <summary>
        /// 梯度惩罚权重 (WGAN-GP)
        /// </summary>
        public double GradientPenaltyWeight { get; set; } = 10.0;

        /// <summary>
        /// 损失函数类型
        /// </summary>
        public LossType LossType { get; set; } = LossType.LSGAN;

        /// <summary>
        /// 是否启用混合精度训练
        /// </summary>
        public bool UseMixedPrecision { get; set; } = false;

        /// <summary>
        /// 验证集比例 (0-1)
        /// </summary>
        public double ValidationSplit { get; set; } = 0.1;

        /// <summary>
        /// 是否在训练时显示进度条
        /// </summary>
        public bool ShowProgress { get; set; } = true;

        /// <summary>
        /// 日志记录间隔 (每多少个batch记录一次)
        /// </summary>
        public int LogInterval { get; set; } = 100;
    }

    /// <summary>
    /// 损失函数类型
    /// </summary>
    public enum LossType
    {
        /// <summary>
        /// 标准GAN损失 (Binary Cross Entropy)
        /// </summary>
        Standard,

        /// <summary>
        /// 最小二乘GAN损失
        /// </summary>
        LSGAN,

        /// <summary>
        /// Wasserstein GAN损失
        /// </summary>
        WGAN,

        /// <summary>
        /// Wasserstein GAN with Gradient Penalty
        /// </summary>
        WGAN_GP,

        /// <summary>
        /// Hinge损失
        /// </summary>
        Hinge
    }
}
