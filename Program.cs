using FloorPlanGAN.Models;
using FloorPlanGAN.Training;
using FloorPlanGAN.Utils;
using TorchSharp;

namespace FloorPlanGAN
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("🏗️ 建筑平面图GAN训练系统");
            Console.WriteLine("================================");
            
            try
            {
                // 检查CUDA可用性
                var device = torch.cuda.is_available() ? torch.CUDA : torch.CPU;
                Console.WriteLine($"使用设备: {device}");
                
                if (device == torch.CUDA)
                {
                    Console.WriteLine($"CUDA设备数量: {torch.cuda.device_count()}");
                    Console.WriteLine($"当前CUDA设备: GPU");
                }
                
                // 解析命令行参数
                var mode = args.Length > 0 ? args[0].ToLower() : "train";
                
                switch (mode)
                {
                    case "train":
                        await TrainModelAsync(args);
                        break;
                    case "generate":
                        await GenerateSamplesAsync(args);
                        break;
                    case "evaluate":
                        await EvaluateModelAsync(args);
                        break;
                    case "demo":
                        await RunDemoAsync();
                        break;
                    case "test":
                        TestProgram.RunBasicTest();
                        break;
                    default:
                        ShowUsage();
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 程序执行失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 训练模型
        /// </summary>
        static async Task TrainModelAsync(string[] args)
        {
            Console.WriteLine("\n🚀 开始训练模型...");
            
            // 创建训练配置
            var options = CreateTrainingOptions(args);
            PrintTrainingOptions(options);
            
            // 验证数据集
            if (!Directory.Exists(options.DataRoot))
            {
                Console.WriteLine($"❌ 数据集目录不存在: {options.DataRoot}");
                Console.WriteLine("\n📁 请按以下结构准备数据:");
                Console.WriteLine("DataRoot/");
                Console.WriteLine("├── outlines/     # 外轮廓图片");
                Console.WriteLine("│   ├── 001.png");
                Console.WriteLine("│   ├── 002.png");
                Console.WriteLine("│   └── ...");
                Console.WriteLine("└── floorplans/   # 对应的平面图");
                Console.WriteLine("    ├── 001.png");
                Console.WriteLine("    ├── 002.png");
                Console.WriteLine("    └── ...");
                return;
            }
            
            // 创建训练器
            var trainer = new FloorPlanGANTrainer(options);
            
            // 开始训练
            await trainer.TrainAsync();
            
            Console.WriteLine("✅ 训练完成!");
        }

        /// <summary>
        /// 生成样本
        /// </summary>
        static async Task GenerateSamplesAsync(string[] args)
        {
            Console.WriteLine("\n🎨 生成样本...");
            
            var checkpointPath = args.Length > 1 ? args[1] : "checkpoints/checkpoint_final.pth";
            var outputDir = args.Length > 2 ? args[2] : "generated_samples";
            var sampleCount = args.Length > 3 ? int.Parse(args[3]) : 10;
            
            if (!File.Exists(checkpointPath))
            {
                Console.WriteLine($"❌ 检查点文件不存在: {checkpointPath}");
                return;
            }
            
            // 创建配置
            var options = new FloorPlanGANOptions();
            
            // 创建训练器并加载模型
            var trainer = new FloorPlanGANTrainer(options);

            // 假设检查点路径是目录，查找最新的模型文件
            var checkpointDir = Path.GetDirectoryName(checkpointPath) ?? "checkpoints";
            var generatorFiles = Directory.GetFiles(checkpointDir, "generator_epoch_*.pth");
            if (generatorFiles.Length > 0)
            {
                var latestGenerator = generatorFiles.OrderByDescending(f => f).First();
                var epoch = ExtractEpochFromFilename(latestGenerator);
                trainer.LoadCheckpoint(checkpointDir, epoch);
            }
            else
            {
                Console.WriteLine("未找到生成器模型文件");
                return;
            }
            
            // 生成样本
            Directory.CreateDirectory(outputDir);
            
            // 这里需要一些测试外轮廓图像
            Console.WriteLine($"生成 {sampleCount} 个样本到 {outputDir}");
            
            Console.WriteLine("✅ 样本生成完成!");
        }

        /// <summary>
        /// 评估模型
        /// </summary>
        static async Task EvaluateModelAsync(string[] args)
        {
            Console.WriteLine("\n📊 评估模型...");
            
            var checkpointPath = args.Length > 1 ? args[1] : "checkpoints/checkpoint_final.pth";
            
            if (!File.Exists(checkpointPath))
            {
                Console.WriteLine($"❌ 检查点文件不存在: {checkpointPath}");
                return;
            }
            
            // 创建配置
            var options = new FloorPlanGANOptions();
            
            // 创建训练器并加载模型
            var trainer = new FloorPlanGANTrainer(options);

            var checkpointDir = Path.GetDirectoryName(checkpointPath) ?? "checkpoints";
            var generatorFiles = Directory.GetFiles(checkpointDir, "generator_epoch_*.pth");
            if (generatorFiles.Length > 0)
            {
                var latestGenerator = generatorFiles.OrderByDescending(f => f).First();
                var epoch = ExtractEpochFromFilename(latestGenerator);
                trainer.LoadCheckpoint(checkpointDir, epoch);
            }
            else
            {
                Console.WriteLine("未找到生成器模型文件");
                return;
            }
            
            // 评估模型
            var metrics = await trainer.EvaluateAsync();
            
            Console.WriteLine("评估结果:");
            foreach (var metric in metrics)
            {
                Console.WriteLine($"  {metric.Key}: {metric.Value:F4}");
            }
            
            Console.WriteLine("✅ 评估完成!");
        }

        /// <summary>
        /// 运行演示
        /// </summary>
        static async Task RunDemoAsync()
        {
            Console.WriteLine("\n🎯 运行演示...");
            
            // 创建演示数据
            await CreateDemoDataAsync();
            
            // 使用演示数据训练一个小模型
            var options = new FloorPlanGANOptions
            {
                DataRoot = "demo_data",
                BatchSize = 4,
                NumEpochs = 10,
                ImageSize = 128,
                SaveInterval = 5,
                SampleInterval = 2
            };
            
            var trainer = new FloorPlanGANTrainer(options);
            await trainer.TrainAsync();
            
            Console.WriteLine("✅ 演示完成!");
        }

        /// <summary>
        /// 创建训练配置
        /// </summary>
        static FloorPlanGANOptions CreateTrainingOptions(string[] args)
        {
            var options = new FloorPlanGANOptions();
            
            // 从命令行参数解析配置
            for (int i = 1; i < args.Length; i += 2)
            {
                if (i + 1 >= args.Length) break;
                
                var key = args[i].ToLower();
                var value = args[i + 1];
                
                switch (key)
                {
                    case "--data":
                        options.DataRoot = value;
                        break;
                    case "--batch-size":
                        options.BatchSize = int.Parse(value);
                        break;
                    case "--epochs":
                        options.NumEpochs = int.Parse(value);
                        break;
                    case "--lr":
                        options.LearningRate = double.Parse(value);
                        break;
                    case "--image-size":
                        options.ImageSize = int.Parse(value);
                        break;
                    case "--checkpoint-dir":
                        options.CheckpointDir = value;
                        break;
                    case "--output-dir":
                        options.OutputDir = value;
                        break;
                }
            }
            
            return options;
        }

        /// <summary>
        /// 打印训练配置
        /// </summary>
        static void PrintTrainingOptions(FloorPlanGANOptions options)
        {
            Console.WriteLine("\n⚙️ 训练配置:");
            Console.WriteLine($"  数据集路径: {options.DataRoot}");
            Console.WriteLine($"  批次大小: {options.BatchSize}");
            Console.WriteLine($"  训练轮数: {options.NumEpochs}");
            Console.WriteLine($"  学习率: {options.LearningRate}");
            Console.WriteLine($"  图像尺寸: {options.ImageSize}x{options.ImageSize}");
            Console.WriteLine($"  损失函数: {options.LossType}");
            Console.WriteLine($"  检查点目录: {options.CheckpointDir}");
            Console.WriteLine($"  输出目录: {options.OutputDir}");
            Console.WriteLine($"  设备: {options.Device}");
        }

        /// <summary>
        /// 创建演示数据
        /// </summary>
        static Task CreateDemoDataAsync()
        {
            Console.WriteLine("创建演示数据...");

            var demoDir = "demo_data";
            var outlinesDir = Path.Combine(demoDir, "outlines");
            var floorplansDir = Path.Combine(demoDir, "floorplans");

            Directory.CreateDirectory(outlinesDir);
            Directory.CreateDirectory(floorplansDir);

            // 这里可以创建一些简单的演示图像
            // 由于简化，我们只创建目录结构

            Console.WriteLine($"演示数据目录已创建: {demoDir}");
            Console.WriteLine("请手动添加一些外轮廓和平面图图像进行测试");

            return Task.CompletedTask;
        }

        /// <summary>
        /// 从文件名提取epoch数字
        /// </summary>
        static int ExtractEpochFromFilename(string filename)
        {
            var match = System.Text.RegularExpressions.Regex.Match(filename, @"epoch_(\d+)");
            return match.Success ? int.Parse(match.Groups[1].Value) : 0;
        }

        /// <summary>
        /// 显示使用说明
        /// </summary>
        static void ShowUsage()
        {
            Console.WriteLine("\n📖 使用说明:");
            Console.WriteLine("FloorPlanGAN.exe <mode> [options]");
            Console.WriteLine();
            Console.WriteLine("模式:");
            Console.WriteLine("  train      - 训练模型");
            Console.WriteLine("  generate   - 生成样本");
            Console.WriteLine("  evaluate   - 评估模型");
            Console.WriteLine("  demo       - 运行演示");
            Console.WriteLine("  test       - 运行基础测试");
            Console.WriteLine();
            Console.WriteLine("训练选项:");
            Console.WriteLine("  --data <path>           数据集路径");
            Console.WriteLine("  --batch-size <int>      批次大小 (默认: 16)");
            Console.WriteLine("  --epochs <int>          训练轮数 (默认: 200)");
            Console.WriteLine("  --lr <float>            学习率 (默认: 0.0002)");
            Console.WriteLine("  --image-size <int>      图像尺寸 (默认: 256)");
            Console.WriteLine("  --checkpoint-dir <path> 检查点目录");
            Console.WriteLine("  --output-dir <path>     输出目录");
            Console.WriteLine();
            Console.WriteLine("示例:");
            Console.WriteLine("  FloorPlanGAN.exe train --data \"D:/FloorPlanData\" --epochs 100");
            Console.WriteLine("  FloorPlanGAN.exe generate \"checkpoints/checkpoint_final.pth\"");
            Console.WriteLine("  FloorPlanGAN.exe demo");
        }
    }
}
