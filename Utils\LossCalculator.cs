using FloorPlanGAN.Models;
using TorchSharp;
using static TorchSharp.torch;
using static TorchSharp.torch.nn;

namespace FloorPlanGAN.Utils
{
    /// <summary>
    /// 损失函数计算器
    /// </summary>
    public class LossCalculator
    {
        private readonly LossType _lossType;

        public LossCalculator(LossType lossType)
        {
            _lossType = lossType;
        }

        /// <summary>
        /// 计算判别器损失
        /// </summary>
        public Tensor CalculateDiscriminatorLoss(Tensor output, Tensor labels, bool isReal)
        {
            return _lossType switch
            {
                LossType.Standard => CalculateStandardDiscriminatorLoss(output, labels),
                LossType.LSGAN => CalculateLSGANDiscriminatorLoss(output, labels),
                LossType.WGAN => CalculateWGANDiscriminatorLoss(output, isReal),
                LossType.WGAN_GP => CalculateWGANDiscriminatorLoss(output, isReal),
                LossType.Hinge => CalculateHingeDiscriminatorLoss(output, isReal),
                _ => throw new ArgumentException($"不支持的损失类型: {_lossType}")
            };
        }

        /// <summary>
        /// 计算生成器损失
        /// </summary>
        public Tensor CalculateGeneratorLoss(Tensor output, Tensor labels)
        {
            return _lossType switch
            {
                LossType.Standard => CalculateStandardGeneratorLoss(output, labels),
                LossType.LSGAN => CalculateLSGANGeneratorLoss(output, labels),
                LossType.WGAN => CalculateWGANGeneratorLoss(output),
                LossType.WGAN_GP => CalculateWGANGeneratorLoss(output),
                LossType.Hinge => CalculateHingeGeneratorLoss(output),
                _ => throw new ArgumentException($"不支持的损失类型: {_lossType}")
            };
        }

        #region 标准GAN损失 (Binary Cross Entropy)

        private Tensor CalculateStandardDiscriminatorLoss(Tensor output, Tensor labels)
        {
            // 使用简化的二元交叉熵损失
            return -(labels * torch.log(output + 1e-8) + (1 - labels) * torch.log(1 - output + 1e-8)).mean();
        }

        private Tensor CalculateStandardGeneratorLoss(Tensor output, Tensor labels)
        {
            return -(labels * torch.log(output + 1e-8) + (1 - labels) * torch.log(1 - output + 1e-8)).mean();
        }

        #endregion

        #region LSGAN损失 (Least Squares)

        private Tensor CalculateLSGANDiscriminatorLoss(Tensor output, Tensor labels)
        {
            return torch.pow(output - labels, 2).mean();
        }

        private Tensor CalculateLSGANGeneratorLoss(Tensor output, Tensor labels)
        {
            return torch.pow(output - labels, 2).mean();
        }

        #endregion

        #region WGAN损失 (Wasserstein)

        private Tensor CalculateWGANDiscriminatorLoss(Tensor output, bool isReal)
        {
            if (isReal)
            {
                return -output.mean(); // 最大化真实样本的输出
            }
            else
            {
                return output.mean(); // 最小化假样本的输出
            }
        }

        private Tensor CalculateWGANGeneratorLoss(Tensor output)
        {
            return -output.mean(); // 最大化假样本的输出
        }

        #endregion

        #region Hinge损失

        private Tensor CalculateHingeDiscriminatorLoss(Tensor output, bool isReal)
        {
            if (isReal)
            {
                return torch.clamp(1.0 - output, min: 0.0).mean();
            }
            else
            {
                return torch.clamp(1.0 + output, min: 0.0).mean();
            }
        }

        private Tensor CalculateHingeGeneratorLoss(Tensor output)
        {
            return -output.mean();
        }

        #endregion

        /// <summary>
        /// 计算感知损失 (Perceptual Loss)
        /// 使用预训练的VGG网络提取特征
        /// </summary>
        public Tensor CalculatePerceptualLoss(Tensor real, Tensor fake, string featureLayer = "relu3_3")
        {
            // 注意: 这里需要实现VGG特征提取器
            // 由于TorchSharp的限制，这里提供一个简化版本
            
            // 简化的感知损失：直接使用像素级L1损失
            return torch.abs(real - fake).mean();
        }

        /// <summary>
        /// 计算特征匹配损失 (Feature Matching Loss)
        /// </summary>
        public Tensor CalculateFeatureMatchingLoss(List<Tensor> realFeatures, List<Tensor> fakeFeatures)
        {
            if (realFeatures.Count != fakeFeatures.Count)
            {
                throw new ArgumentException("真实特征和假特征的数量必须相同");
            }

            var totalLoss = torch.tensor(0.0f, device: realFeatures[0].device);

            for (int i = 0; i < realFeatures.Count; i++)
            {
                var featureLoss = torch.abs(realFeatures[i] - fakeFeatures[i]).mean();
                totalLoss += featureLoss;
            }

            return totalLoss / realFeatures.Count;
        }

        /// <summary>
        /// 计算总变分损失 (Total Variation Loss)
        /// 用于平滑生成的图像
        /// </summary>
        public Tensor CalculateTotalVariationLoss(Tensor images)
        {
            var batchSize = images.shape[0];
            var height = images.shape[2];
            var width = images.shape[3];

            // 水平方向的变分
            var horizontalVariation = torch.abs(
                images.narrow(3, 0, width - 1) - images.narrow(3, 1, width - 1)
            ).sum();

            // 垂直方向的变分
            var verticalVariation = torch.abs(
                images.narrow(2, 0, height - 1) - images.narrow(2, 1, height - 1)
            ).sum();

            return (horizontalVariation + verticalVariation) / batchSize;
        }

        /// <summary>
        /// 计算SSIM损失 (结构相似性) - 简化版本
        /// </summary>
        public Tensor CalculateSSIMLoss(Tensor real, Tensor fake, double windowSize = 11, double sigma = 1.5)
        {
            // 极简化的SSIM实现 - 使用MSE作为替代
            return torch.pow(real - fake, 2).mean();
        }

        /// <summary>
        /// 计算对抗损失权重
        /// 在训练过程中动态调整对抗损失的权重
        /// </summary>
        public double CalculateAdversarialWeight(int currentEpoch, int totalEpochs, double initialWeight = 1.0)
        {
            // 在训练初期降低对抗损失的权重，让生成器先学会基本的图像生成
            var progress = (double)currentEpoch / totalEpochs;
            
            if (progress < 0.1) // 前10%的训练
            {
                return initialWeight * 0.1;
            }
            else if (progress < 0.3) // 前30%的训练
            {
                return initialWeight * 0.5;
            }
            else
            {
                return initialWeight;
            }
        }

        /// <summary>
        /// 计算梯度惩罚 (用于WGAN-GP)
        /// </summary>
        public Tensor CalculateGradientPenalty(Tensor realData, Tensor fakeData, 
                                             Func<Tensor, Tensor> discriminator, 
                                             torch.Device device, double lambda = 10.0)
        {
            var batchSize = realData.shape[0];
            
            // 生成随机插值系数
            var alpha = torch.rand(batchSize, 1, 1, 1, device: device);
            
            // 插值
            var interpolated = alpha * realData + (1 - alpha) * fakeData;
            interpolated.requires_grad_(true);
            
            // 计算判别器输出
            var dInterpolated = discriminator(interpolated);
            
            // 计算梯度
            var gradients = torch.autograd.grad(
                outputs: (IList<Tensor>)dInterpolated,
                inputs: (IList<Tensor>)interpolated,
                grad_outputs: (IList<Tensor>)torch.ones_like(dInterpolated),
                create_graph: true,
                retain_graph: true
            )[0];
            
            // 计算梯度范数
            var gradientNorm = gradients.view(batchSize, -1).norm(2);
            
            // 梯度惩罚
            var gradientPenalty = ((gradientNorm - 1).pow(2)).mean() * lambda;
            
            return gradientPenalty;
        }
    }
}
