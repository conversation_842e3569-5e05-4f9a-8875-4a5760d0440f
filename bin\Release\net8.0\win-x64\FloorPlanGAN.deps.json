{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"FloorPlanGAN/1.0.0": {"dependencies": {"Microsoft.NET.ILLink.Tasks": "8.0.19", "SixLabors.ImageSharp": "3.1.4", "System.Drawing.Common": "8.0.8", "TorchSharp": "0.102.7", "TorchSharp-cuda-windows": "0.102.7", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.19"}, "runtime": {"FloorPlanGAN.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.19": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.1925.36514"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "8.0.1925.36514"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "8.0.1925.36514"}, "clretwrc.dll": {"fileVersion": "8.0.1925.36514"}, "clrgc.dll": {"fileVersion": "8.0.1925.36514"}, "clrjit.dll": {"fileVersion": "8.0.1925.36514"}, "coreclr.dll": {"fileVersion": "8.0.1925.36514"}, "createdump.exe": {"fileVersion": "8.0.1925.36514"}, "hostfxr.dll": {"fileVersion": "8.0.1925.36514"}, "hostpolicy.dll": {"fileVersion": "8.0.1925.36514"}, "mscordaccore.dll": {"fileVersion": "8.0.1925.36514"}, "mscordaccore_amd64_amd64_8.0.1925.36514.dll": {"fileVersion": "8.0.1925.36514"}, "mscordbi.dll": {"fileVersion": "8.0.1925.36514"}, "mscorrc.dll": {"fileVersion": "8.0.1925.36514"}, "msquic.dll": {"fileVersion": "*******"}}}, "Google.Protobuf/3.21.9": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "libtorch-cuda-12.1-win-x64/*******": {"dependencies": {"libtorch-cuda-12.1-win-x64-part1": "*******", "libtorch-cuda-12.1-win-x64-part10": "*******", "libtorch-cuda-12.1-win-x64-part2": "*******", "libtorch-cuda-12.1-win-x64-part3-fragment1": "*******", "libtorch-cuda-12.1-win-x64-part3-primary": "*******", "libtorch-cuda-12.1-win-x64-part4": "*******", "libtorch-cuda-12.1-win-x64-part5-fragment1": "*******", "libtorch-cuda-12.1-win-x64-part5-primary": "*******", "libtorch-cuda-12.1-win-x64-part6": "*******", "libtorch-cuda-12.1-win-x64-part7": "*******", "libtorch-cuda-12.1-win-x64-part8": "*******", "libtorch-cuda-12.1-win-x64-part9-fragment1": "*******", "libtorch-cuda-12.1-win-x64-part9-fragment2": "*******", "libtorch-cuda-12.1-win-x64-part9-primary": "*******"}}, "libtorch-cuda-12.1-win-x64-part1/*******": {"native": {"runtimes/win-x64/native/asmjit.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/c10.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/c10_cuda.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/caffe2_nvrtc.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/cublas64_12.dll": {"fileVersion": "6.14.11.1213"}, "runtimes/win-x64/native/cudart64_12.dll": {"fileVersion": "6.14.11.12010"}, "runtimes/win-x64/native/cudnn64_8.dll": {"fileVersion": "6.14.11.6050"}, "runtimes/win-x64/native/nvJitLink_120_0.dll": {"fileVersion": "6.14.11.9000"}, "runtimes/win-x64/native/nvrtc64_120_0.dll": {"fileVersion": "6.14.11.9000"}, "runtimes/win-x64/native/torch_global_deps.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/uv.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/zlibwapi.dll": {"fileVersion": "1.2.3.0"}}}, "libtorch-cuda-12.1-win-x64-part10/*******": {"native": {"runtimes/win-x64/native/cufft64_11.dll": {"fileVersion": "6.14.11.1102"}, "runtimes/win-x64/native/cufftw64_11.dll": {"fileVersion": "6.14.11.1102"}, "runtimes/win-x64/native/cupti64_2023.1.1.dll": {"fileVersion": "2023.1.1.0"}, "runtimes/win-x64/native/curand64_10.dll": {"fileVersion": "6.14.11.1032"}}}, "libtorch-cuda-12.1-win-x64-part2/*******": {"native": {"runtimes/win-x64/native/cudnn_adv_infer64_8.dll": {"fileVersion": "6.14.11.12000"}, "runtimes/win-x64/native/cudnn_adv_train64_8.dll": {"fileVersion": "6.14.11.12000"}}}, "libtorch-cuda-12.1-win-x64-part3-fragment1/*******": {}, "libtorch-cuda-12.1-win-x64-part3-primary/*******": {"native": {"runtimes/win-x64/native/cudnn_cnn_infer64_8.dll": {"fileVersion": "6.14.11.12000"}, "runtimes/win-x64/native/cudnn_cnn_infer64_8.dll.sha": {"fileVersion": "0.0.0.0"}}}, "libtorch-cuda-12.1-win-x64-part4/*******": {"native": {"runtimes/win-x64/native/cudnn_cnn_train64_8.dll": {"fileVersion": "6.14.11.12000"}, "runtimes/win-x64/native/cudnn_ops_infer64_8.dll": {"fileVersion": "6.14.11.12000"}, "runtimes/win-x64/native/cudnn_ops_train64_8.dll": {"fileVersion": "6.14.11.12000"}}}, "libtorch-cuda-12.1-win-x64-part5-fragment1/*******": {}, "libtorch-cuda-12.1-win-x64-part5-primary/*******": {"native": {"runtimes/win-x64/native/cublasLt64_12.dll": {"fileVersion": "6.14.11.1213"}, "runtimes/win-x64/native/cublasLt64_12.dll.sha": {"fileVersion": "0.0.0.0"}}}, "libtorch-cuda-12.1-win-x64-part6/*******": {"native": {"runtimes/win-x64/native/cusolver64_11.dll": {"fileVersion": "6.14.11.1145"}, "runtimes/win-x64/native/cusolverMg64_11.dll": {"fileVersion": "6.14.11.1145"}, "runtimes/win-x64/native/fbgemm.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/fbjni.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libiomp5md.dll": {"fileVersion": "5.0.2023.118"}, "runtimes/win-x64/native/libiompstubs5md.dll": {"fileVersion": "5.0.2023.118"}, "runtimes/win-x64/native/nvToolsExt64_1.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/nvrtc-builtins64_121.dll": {"fileVersion": "0.0.0.0"}}}, "libtorch-cuda-12.1-win-x64-part7/*******": {"native": {"runtimes/win-x64/native/pytorch_jni.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/torch.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/torch_cpu.dll": {"fileVersion": "0.0.0.0"}}}, "libtorch-cuda-12.1-win-x64-part8/*******": {"native": {"runtimes/win-x64/native/cusparse64_12.dll": {"fileVersion": "6.14.11.1210"}}}, "libtorch-cuda-12.1-win-x64-part9-fragment1/*******": {}, "libtorch-cuda-12.1-win-x64-part9-fragment2/*******": {}, "libtorch-cuda-12.1-win-x64-part9-primary/*******": {"native": {"runtimes/win-x64/native/torch_cuda.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/torch_cuda.dll.sha": {"fileVersion": "0.0.0.0"}}}, "Microsoft.NET.ILLink.Tasks/8.0.19": {}, "Microsoft.Win32.SystemEvents/8.0.0": {"runtime": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "SharpZipLib/1.4.0": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.ImageSharp/3.1.4": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkiaSharp/2.88.6": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.6", "SkiaSharp.NativeAssets.macOS": "2.88.6"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp.NativeAssets.macOS/2.88.6": {}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"native": {"runtimes/win-x64/native/libSkiaSharp.dll": {"fileVersion": "0.0.0.0"}}}, "System.Drawing.Common/8.0.8": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}}}, "System.Memory/4.5.5": {}, "TorchSharp/0.102.7": {"dependencies": {"Google.Protobuf": "3.21.9", "SharpZipLib": "1.4.0", "SkiaSharp": "2.88.6", "System.Memory": "4.5.5"}, "runtime": {"lib/net6.0/TorchSharp.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "native": {"runtimes/win-x64/native/LibTorchSharp.dll": {"fileVersion": "0.0.0.0"}}}, "TorchSharp-cuda-windows/0.102.7": {"dependencies": {"TorchSharp": "0.102.7", "libtorch-cuda-12.1-win-x64": "*******"}}}}, "libraries": {"FloorPlanGAN/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.19": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Google.Protobuf/3.21.9": {"type": "package", "serviceable": true, "sha512": "sha512-OTpFujTgkmqMLbg3KT7F/iuKi1rg6s5FCS2M9XcVLDn40zL8wgXm37CY/F6MeOEXKjdcnXGCN/h7oyMkVydVsg==", "path": "google.protobuf/3.21.9", "hashPath": "google.protobuf.3.21.9.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64/*******": {"type": "package", "serviceable": true, "sha512": "sha512-fSFsCwBtQqoLV5Ede1fsymQHU1Fw50JN5A+29hu+IHuTOskFLM7AUUFTm8ljlyVghlC8CjsuYhjFSQoyt8PGXQ==", "path": "libtorch-cuda-12.1-win-x64/*******", "hashPath": "libtorch-cuda-12.1-win-x64.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part1/*******": {"type": "package", "serviceable": true, "sha512": "sha512-FJuypx3MUPbbMWDqj0vxAcQs7Xs9GK2D1ed1BeqN10lt7MpsTyXUORg8pxokzbbjEBlnf0wC8d25JN1oL46l4w==", "path": "libtorch-cuda-12.1-win-x64-part1/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part1.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part10/*******": {"type": "package", "serviceable": true, "sha512": "sha512-Pl/4S1P5Axgs7VAsdIm+zXwyd238zsKqOHnL6alNGF6UmjW4scSfLvemzQ80iK08VSqUOo2Zf2PDRD4T2iAyoA==", "path": "libtorch-cuda-12.1-win-x64-part10/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part10.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part2/*******": {"type": "package", "serviceable": true, "sha512": "sha512-OcnU8im9yaNVY7aaCLPw8olPNsjW/WEwR8h2ZY/qLgGsXBtGxzfan+2xewgt+JnBclmaNdCZjKTNlQl+53ZxFQ==", "path": "libtorch-cuda-12.1-win-x64-part2/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part2.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part3-fragment1/*******": {"type": "package", "serviceable": true, "sha512": "sha512-AJbZauBUa/H40PdfMRPURtgAK/UAXoC15zsYW0Rh1tN6U5LRny/LzU+B9rOCHFajHwgU/E/WjVqWJhybR5dumw==", "path": "libtorch-cuda-12.1-win-x64-part3-fragment1/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part3-fragment1.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part3-primary/*******": {"type": "package", "serviceable": true, "sha512": "sha512-FZFdU5C1CgfJXy/Z07aVJzxF0so3c6MBLiibP4x+6GbXUt/gdZAQPfAQDs3U5eLX0R7OcIVBZcz+ib4QjoA+cA==", "path": "libtorch-cuda-12.1-win-x64-part3-primary/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part3-primary.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part4/*******": {"type": "package", "serviceable": true, "sha512": "sha512-AvVKciGKmsKGXD+hEYe+nrBSjWdGT1XIILEHAPswyJtVscER1aoz+CySYxhkaWIWrsI354W7nUZwxcCiKu3dWQ==", "path": "libtorch-cuda-12.1-win-x64-part4/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part4.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part5-fragment1/*******": {"type": "package", "serviceable": true, "sha512": "sha512-x6MF6VfuqXiPhfAKld91fSnaX3V/p9MWTIvMbmzyC7MIpF+uCvKMCuVEhgC0aeIlFEkPCNq7KAY9zxYPrWVLEw==", "path": "libtorch-cuda-12.1-win-x64-part5-fragment1/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part5-fragment1.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part5-primary/*******": {"type": "package", "serviceable": true, "sha512": "sha512-qdOSsyO6F+XLhS6OMd/oMeQc0s41NUKeluMelyX5+bj6btOnQy/KshWdUqDI+KpjY2zG/FOwNKBMNjf3/KFT5Q==", "path": "libtorch-cuda-12.1-win-x64-part5-primary/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part5-primary.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part6/*******": {"type": "package", "serviceable": true, "sha512": "sha512-08Bsxlwp2I7K0oKgwDUC+Y6M55Nge8Pa7z9/gvRHrM5nv4stSWUN3V7t8WVeZfiSqj0iXeLNlP3eynt6qBiRzg==", "path": "libtorch-cuda-12.1-win-x64-part6/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part6.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part7/*******": {"type": "package", "serviceable": true, "sha512": "sha512-vBLDVpx5wgCoqSV6ncYTFzJkVwr5/iWJC+y1HliLy5270iOD/lQ2WHNBbqy8YciaRYRYLKOQEha/yMJG542b4g==", "path": "libtorch-cuda-12.1-win-x64-part7/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part7.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part8/*******": {"type": "package", "serviceable": true, "sha512": "sha512-WTS+eU2qW4B3mkW8dUwisj1wBzg0krMD0fV+Ryq9a6eBqsT2y/NKTL4+567unvwxDaAqJBfC7nLNgVBwLcNgGA==", "path": "libtorch-cuda-12.1-win-x64-part8/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part8.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part9-fragment1/*******": {"type": "package", "serviceable": true, "sha512": "sha512-ij3t/I+kkVrBFSuB1/S3lNKblHeiLLlyhrgoRwu2m8mAPEJoioHO5H/4sSM0UDp3YRYp31EAlK1e+pkHsCTZiw==", "path": "libtorch-cuda-12.1-win-x64-part9-fragment1/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part9-fragment1.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part9-fragment2/*******": {"type": "package", "serviceable": true, "sha512": "sha512-cOqLuqdig1+Ra6REXB44Hmou7xL2ZYESxPrTS0MFocwUz1h31Q4YVw4a0YUQpWf/rx8vh26Gb18jRW02/wUlxA==", "path": "libtorch-cuda-12.1-win-x64-part9-fragment2/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part9-fragment2.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part9-primary/*******": {"type": "package", "serviceable": true, "sha512": "sha512-cgI/G0BZeAoXmYtzOqaSApCDse5iYFk5WPqCcq8vwLV3R2Ag5D5eMKz5eKgjD5d0etJmJzloyYFixXp2rHpC7Q==", "path": "libtorch-cuda-12.1-win-x64-part9-primary/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part9-primary.*******.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/8.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-IhHf+zeZiaE5EXRyxILd4qM+Hj9cxV3sa8MpzZgeEhpvaG3a1VEGF6UCaPFLO44Kua3JkLKluE0SWVamS50PlA==", "path": "microsoft.net.illink.tasks/8.0.19", "hashPath": "microsoft.net.illink.tasks.8.0.19.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "SharpZipLib/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-CdkbBSPIpHD8xBlu+8kDJiqc1Tf9iV89BObnqcvEbwysXSj5h1MfaeLgeeaxPZmi7CTJO8FDofBBNxBW0Vml7A==", "path": "sharpziplib/1.4.0", "hashPath": "sharpziplib.1.4.0.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-lFIdxgGDA5iYkUMRFOze7BGLcdpoLFbR+a20kc1W7NepvzU7ejtxtWOg9RvgG7kb9tBoJ3ONYOK6kLil/dgF1w==", "path": "sixlabors.imagesharp/3.1.4", "hashPath": "sixlabors.imagesharp.3.1.4.nupkg.sha512"}, "SkiaSharp/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-wdfeBAQrEQCbJIRgAiargzP1Uy+0grZiG4CSgBnhAgcJTsPzlifIaO73JRdwIlT3TyBoeU9jEqzwFUhl4hTYnQ==", "path": "skiasharp/2.88.6", "hashPath": "skiasharp.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-Sko9LFxRXSjb3OGh5/RxrVRXxYo48tr5NKuuSy6jB85GrYt8WRqVY1iLOLwtjPiVAt4cp+pyD4i30azanS64dw==", "path": "skiasharp.nativeassets.macos/2.88.6", "hashPath": "skiasharp.nativeassets.macos.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-7TzFO0u/g2MpQsTty4fyCDdMcfcWI+aLswwfnYXr3gtNS6VLKdMXPMeKpJa3pJSLnUBN6wD0JjuCe8OoLBQ6cQ==", "path": "skiasharp.nativeassets.win32/2.88.6", "hashPath": "skiasharp.nativeassets.win32.2.88.6.nupkg.sha512"}, "System.Drawing.Common/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4ZM1wvLjz9nVVExsfPAaSl/qOvU+QNedJL5rQ+2Wbow+iGeyO0e7XN07707rMBgaffEeeLrCZBwC0oHUuvRdPw==", "path": "system.drawing.common/8.0.8", "hashPath": "system.drawing.common.8.0.8.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "TorchSharp/0.102.7": {"type": "package", "serviceable": true, "sha512": "sha512-P4bl1QcZxD+ZLrPjblUSv6MjRLlDWKa6loPZMMfekIubiandR2gdloL4cQZmfblxf5JzaUWwsbcoBqMu1uHZCw==", "path": "torchsharp/0.102.7", "hashPath": "torchsharp.0.102.7.nupkg.sha512"}, "TorchSharp-cuda-windows/0.102.7": {"type": "package", "serviceable": true, "sha512": "sha512-7bDTN3mIL0OPBq9fx+TUE4p2ta7Dx7ExrXpNKHMKEFzx1N1165bAmzAPORiQmWsQduDMFb+DsMHJ7AWfVL88wQ==", "path": "torchsharp-cuda-windows/0.102.7", "hashPath": "torchsharp-cuda-windows.0.102.7.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}