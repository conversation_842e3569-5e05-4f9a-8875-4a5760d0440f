{"format": 1, "restore": {"D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj": {}}, "projects": {"D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj", "projectName": "FloorPlanGAN", "projectPath": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.11, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.8, )"}, "TorchSharp": {"target": "Package", "version": "[0.102.7, )"}, "TorchSharp-cuda-windows": {"target": "Package", "version": "[0.102.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}