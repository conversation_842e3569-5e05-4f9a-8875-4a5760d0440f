using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using TorchSharp;
using static TorchSharp.torch;

namespace FloorPlanGAN.Data
{
    /// <summary>
    /// 建筑平面图数据集
    /// 数据结构:
    /// DataRoot/
    /// ├── outlines/     # 外轮廓图片 (条件输入)
    /// │   ├── 001.png
    /// │   ├── 002.png
    /// │   └── ...
    /// └── floorplans/   # 对应的平面图 (目标输出)
    ///     ├── 001.png
    ///     ├── 002.png
    ///     └── ...
    /// </summary>
    public class FloorPlanDataset
    {
        private readonly string _dataRoot;
        private readonly int _imageSize;
        private readonly List<string> _imageFiles;
        private readonly Random _random;

        public int Count => _imageFiles.Count;

        public FloorPlanDataset(string dataRoot, int imageSize = 256)
        {
            _dataRoot = dataRoot;
            _imageSize = imageSize;
            _random = new Random();

            // 检查数据目录
            var outlinesDir = Path.Combine(_dataRoot, "outlines");
            var floorplansDir = Path.Combine(_dataRoot, "floorplans");

            if (!Directory.Exists(outlinesDir))
                throw new DirectoryNotFoundException($"外轮廓目录不存在: {outlinesDir}");

            if (!Directory.Exists(floorplansDir))
                throw new DirectoryNotFoundException($"平面图目录不存在: {floorplansDir}");

            // 获取所有图片文件
            var outlineFiles = Directory.GetFiles(outlinesDir, "*.png")
                .Concat(Directory.GetFiles(outlinesDir, "*.jpg"))
                .Concat(Directory.GetFiles(outlinesDir, "*.jpeg"))
                .ToList();

            // 验证配对文件存在
            _imageFiles = new List<string>();
            foreach (var outlineFile in outlineFiles)
            {
                var fileName = Path.GetFileNameWithoutExtension(outlineFile);
                var floorplanFile = Directory.GetFiles(floorplansDir, $"{fileName}.*").FirstOrDefault();
                
                if (floorplanFile != null)
                {
                    _imageFiles.Add(fileName);
                }
                else
                {
                    Console.WriteLine($"警告: 找不到对应的平面图文件: {fileName}");
                }
            }

            Console.WriteLine($"数据集加载完成: {_imageFiles.Count} 个配对样本");
        }

        /// <summary>
        /// 获取单个样本
        /// </summary>
        public (Tensor outline, Tensor floorplan) GetItem(int index)
        {
            if (index < 0 || index >= _imageFiles.Count)
                throw new ArgumentOutOfRangeException(nameof(index));

            var fileName = _imageFiles[index];
            
            // 加载外轮廓图像
            var outlinePath = GetOutlinePath(fileName);
            var outline = LoadAndPreprocessImage(outlinePath);
            
            // 加载平面图图像
            var floorplanPath = GetFloorplanPath(fileName);
            var floorplan = LoadAndPreprocessImage(floorplanPath);

            return (outline, floorplan);
        }

        /// <summary>
        /// 获取批次数据
        /// </summary>
        public (Tensor outlines, Tensor floorplans) GetBatch(int batchSize, bool shuffle = true)
        {
            var indices = Enumerable.Range(0, _imageFiles.Count).ToList();
            
            if (shuffle)
            {
                // Fisher-Yates洗牌算法
                for (int i = indices.Count - 1; i > 0; i--)
                {
                    int j = _random.Next(i + 1);
                    (indices[i], indices[j]) = (indices[j], indices[i]);
                }
            }

            var selectedIndices = indices.Take(batchSize).ToList();
            
            var outlineList = new List<Tensor>();
            var floorplanList = new List<Tensor>();

            foreach (var index in selectedIndices)
            {
                var (outline, floorplan) = GetItem(index);
                outlineList.Add(outline);
                floorplanList.Add(floorplan);
            }

            var outlines = torch.stack(outlineList.ToArray());
            var floorplans = torch.stack(floorplanList.ToArray());

            return (outlines, floorplans);
        }

        /// <summary>
        /// 创建数据加载器
        /// </summary>
        public IEnumerable<(Tensor outlines, Tensor floorplans)> CreateDataLoader(int batchSize, bool shuffle = true)
        {
            var indices = Enumerable.Range(0, _imageFiles.Count).ToList();
            
            if (shuffle)
            {
                for (int i = indices.Count - 1; i > 0; i--)
                {
                    int j = _random.Next(i + 1);
                    (indices[i], indices[j]) = (indices[j], indices[i]);
                }
            }

            for (int i = 0; i < indices.Count; i += batchSize)
            {
                var batchIndices = indices.Skip(i).Take(batchSize).ToList();
                
                var outlineList = new List<Tensor>();
                var floorplanList = new List<Tensor>();

                foreach (var index in batchIndices)
                {
                    try
                    {
                        var (outline, floorplan) = GetItem(index);
                        outlineList.Add(outline);
                        floorplanList.Add(floorplan);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"加载样本 {index} 失败: {ex.Message}");
                    }
                }

                if (outlineList.Count > 0)
                {
                    var outlines = torch.stack(outlineList.ToArray());
                    var floorplans = torch.stack(floorplanList.ToArray());
                    yield return (outlines, floorplans);
                }
            }
        }

        /// <summary>
        /// 数据增强
        /// </summary>
        public (Tensor outline, Tensor floorplan) ApplyAugmentation(Tensor outline, Tensor floorplan)
        {
            // 随机水平翻转
            if (_random.NextDouble() > 0.5)
            {
                outline = torch.flip(outline, new long[] { 2 }); // 水平翻转
                floorplan = torch.flip(floorplan, new long[] { 2 });
            }

            // 随机垂直翻转
            if (_random.NextDouble() > 0.5)
            {
                outline = torch.flip(outline, new long[] { 1 }); // 垂直翻转
                floorplan = torch.flip(floorplan, new long[] { 1 });
            }

            // 随机旋转 (90度的倍数)
            var rotations = _random.Next(4);
            for (int i = 0; i < rotations; i++)
            {
                outline = torch.rot90(outline, k: 1, dims: (1, 2));
                floorplan = torch.rot90(floorplan, k: 1, dims: (1, 2));
            }

            return (outline, floorplan);
        }

        /// <summary>
        /// 加载并预处理图像
        /// </summary>
        private Tensor LoadAndPreprocessImage(string imagePath)
        {
            using var image = Image.Load<Rgb24>(imagePath);
            
            // 调整大小
            image.Mutate(x => x.Resize(_imageSize, _imageSize));
            
            // 转换为张量
            var tensor = ImageToTensor(image);
            
            // 归一化到 [-1, 1]
            tensor = tensor * 2.0f - 1.0f;
            
            return tensor;
        }

        /// <summary>
        /// 将图像转换为张量
        /// </summary>
        private Tensor ImageToTensor(Image<Rgb24> image)
        {
            var width = image.Width;
            var height = image.Height;
            var data = new float[3 * height * width];

            image.ProcessPixelRows(accessor =>
            {
                for (int y = 0; y < height; y++)
                {
                    var row = accessor.GetRowSpan(y);
                    for (int x = 0; x < width; x++)
                    {
                        var pixel = row[x];
                        var baseIndex = y * width + x;
                        
                        data[baseIndex] = pixel.R / 255.0f;                    // R通道
                        data[height * width + baseIndex] = pixel.G / 255.0f;   // G通道
                        data[2 * height * width + baseIndex] = pixel.B / 255.0f; // B通道
                    }
                }
            });

            return torch.tensor(data).view(3, height, width);
        }

        /// <summary>
        /// 获取外轮廓文件路径
        /// </summary>
        private string GetOutlinePath(string fileName)
        {
            var outlinesDir = Path.Combine(_dataRoot, "outlines");
            var extensions = new[] { ".png", ".jpg", ".jpeg" };
            
            foreach (var ext in extensions)
            {
                var path = Path.Combine(outlinesDir, fileName + ext);
                if (File.Exists(path))
                    return path;
            }
            
            throw new FileNotFoundException($"找不到外轮廓文件: {fileName}");
        }

        /// <summary>
        /// 获取平面图文件路径
        /// </summary>
        private string GetFloorplanPath(string fileName)
        {
            var floorplansDir = Path.Combine(_dataRoot, "floorplans");
            var extensions = new[] { ".png", ".jpg", ".jpeg" };
            
            foreach (var ext in extensions)
            {
                var path = Path.Combine(floorplansDir, fileName + ext);
                if (File.Exists(path))
                    return path;
            }
            
            throw new FileNotFoundException($"找不到平面图文件: {fileName}");
        }

        /// <summary>
        /// 验证数据集
        /// </summary>
        public void ValidateDataset()
        {
            Console.WriteLine("验证数据集...");
            
            var errors = new List<string>();
            
            for (int i = 0; i < Math.Min(10, _imageFiles.Count); i++)
            {
                try
                {
                    var (outline, floorplan) = GetItem(i);
                    
                    if (outline.shape[0] != 3 || outline.shape[1] != _imageSize || outline.shape[2] != _imageSize)
                    {
                        errors.Add($"外轮廓图像 {_imageFiles[i]} 尺寸不正确: {outline.shape}");
                    }
                    
                    if (floorplan.shape[0] != 3 || floorplan.shape[1] != _imageSize || floorplan.shape[2] != _imageSize)
                    {
                        errors.Add($"平面图图像 {_imageFiles[i]} 尺寸不正确: {floorplan.shape}");
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"加载样本 {_imageFiles[i]} 失败: {ex.Message}");
                }
            }
            
            if (errors.Count > 0)
            {
                Console.WriteLine("数据集验证发现问题:");
                foreach (var error in errors)
                {
                    Console.WriteLine($"  - {error}");
                }
            }
            else
            {
                Console.WriteLine("数据集验证通过!");
            }
        }
    }
}
