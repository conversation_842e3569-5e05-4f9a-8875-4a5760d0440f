﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.1</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)torchsharp\0.102.7\buildTransitive\net6.0\TorchSharp.props" Condition="Exists('$(NuGetPackageRoot)torchsharp\0.102.7\buildTransitive\net6.0\TorchSharp.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-primary.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-primary.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-fragment2\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-fragment2.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-fragment2\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-fragment2.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-fragment1.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-fragment1.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part8\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part8.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part8\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part8.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part7\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part7.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part7\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part7.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part6\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part6.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part6\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part6.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part5-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part5-primary.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part5-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part5-primary.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part5-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part5-fragment1.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part5-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part5-fragment1.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part4\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part4.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part4\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part4.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part3-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part3-primary.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part3-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part3-primary.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part3-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part3-fragment1.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part3-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part3-fragment1.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part2\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part2.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part2\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part2.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part10\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part10.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part10\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part10.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part1.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part1.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64.props')" />
    <Import Project="$(NuGetPackageRoot)torchsharp-cuda-windows\0.102.7\buildTransitive\net6.0\TorchSharp-cuda-windows.props" Condition="Exists('$(NuGetPackageRoot)torchsharp-cuda-windows\0.102.7\buildTransitive\net6.0\TorchSharp-cuda-windows.props')" />
    <Import Project="$(NuGetPackageRoot)sixlabors.imagesharp\3.1.11\build\SixLabors.ImageSharp.props" Condition="Exists('$(NuGetPackageRoot)sixlabors.imagesharp\3.1.11\build\SixLabors.ImageSharp.props')" />
  </ImportGroup>
</Project>