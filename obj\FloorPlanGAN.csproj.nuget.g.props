﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)torchsharp\0.101.5\buildTransitive\net6.0\TorchSharp.props" Condition="Exists('$(NuGetPackageRoot)torchsharp\0.101.5\buildTransitive\net6.0\TorchSharp.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cpu-win-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-win-x64.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cpu-win-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-win-x64.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cpu-osx-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-osx-x64.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cpu-osx-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-osx-x64.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cpu-linux-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-linux-x64.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cpu-linux-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-linux-x64.props')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cpu\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu.props" Condition="Exists('$(NuGetPackageRoot)libtorch-cpu\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu.props')" />
    <Import Project="$(NuGetPackageRoot)torchsharp-cpu\0.101.5\buildTransitive\net6.0\TorchSharp-cpu.props" Condition="Exists('$(NuGetPackageRoot)torchsharp-cpu\0.101.5\buildTransitive\net6.0\TorchSharp-cpu.props')" />
    <Import Project="$(NuGetPackageRoot)sixlabors.imagesharp\3.1.11\build\SixLabors.ImageSharp.props" Condition="Exists('$(NuGetPackageRoot)sixlabors.imagesharp\3.1.11\build\SixLabors.ImageSharp.props')" />
  </ImportGroup>
</Project>