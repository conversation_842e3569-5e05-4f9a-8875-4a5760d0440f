{"Version": 1, "WorkspaceRootPath": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|d:\\mywork\\05算法研究\\01代码\\01神经网络\\floorplangan\\training\\floorplangantrainer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|solutionrelative:training\\floorplangantrainer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|d:\\mywork\\05算法研究\\01代码\\01神经网络\\floorplangan\\utils\\imageutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|solutionrelative:utils\\imageutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|d:\\mywork\\05算法研究\\01代码\\01神经网络\\floorplangan\\utils\\losscalculator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|solutionrelative:utils\\losscalculator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|d:\\mywork\\05算法研究\\01代码\\01神经网络\\floorplangan\\models\\conditionaldiscriminator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|solutionrelative:models\\conditionaldiscriminator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|d:\\mywork\\05算法研究\\01代码\\01神经网络\\floorplangan\\models\\conditionalgenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|solutionrelative:models\\conditionalgenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|d:\\mywork\\05算法研究\\01代码\\01神经网络\\floorplangan\\models\\floorplanganoptions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|solutionrelative:models\\floorplanganoptions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|d:\\mywork\\05算法研究\\01代码\\01神经网络\\floorplangan\\floorplangan.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|solutionrelative:floorplangan.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|d:\\mywork\\05算法研究\\01代码\\01神经网络\\floorplangan\\data\\floorplandataset.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{420A3074-547D-DB30-F87D-7A52774682E1}|FloorPlanGAN.csproj|solutionrelative:data\\floorplandataset.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 10, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "LossCalculator.cs", "DocumentMoniker": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Utils\\LossCalculator.cs", "RelativeDocumentMoniker": "Utils\\LossCalculator.cs", "ToolTip": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Utils\\LossCalculator.cs", "RelativeToolTip": "Utils\\LossCalculator.cs", "ViewState": "AgIAAOYAAAAAAAAAAIA5wPcAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T13:32:27.925Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "FloorPlanGANOptions.cs", "DocumentMoniker": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Models\\FloorPlanGANOptions.cs", "RelativeDocumentMoniker": "Models\\FloorPlanGANOptions.cs", "ToolTip": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Models\\FloorPlanGANOptions.cs", "RelativeToolTip": "Models\\FloorPlanGANOptions.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T13:26:22.12Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "FloorPlanGAN", "DocumentMoniker": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj", "RelativeDocumentMoniker": "FloorPlanGAN.csproj", "ToolTip": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj", "RelativeToolTip": "FloorPlanGAN.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-08-21T13:24:44.479Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "FloorPlanDataset.cs", "DocumentMoniker": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Data\\FloorPlanDataset.cs", "RelativeDocumentMoniker": "Data\\FloorPlanDataset.cs", "ToolTip": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Data\\FloorPlanDataset.cs", "RelativeToolTip": "Data\\FloorPlanDataset.cs", "ViewState": "AgIAAAYAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T13:14:17.276Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ConditionalGenerator.cs", "DocumentMoniker": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Models\\ConditionalGenerator.cs", "RelativeDocumentMoniker": "Models\\ConditionalGenerator.cs", "ToolTip": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Models\\ConditionalGenerator.cs", "RelativeToolTip": "Models\\ConditionalGenerator.cs", "ViewState": "AgIAABwAAAAAAAAAAAA5wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T13:14:08.805Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ImageUtils.cs", "DocumentMoniker": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Utils\\ImageUtils.cs", "RelativeDocumentMoniker": "Utils\\ImageUtils.cs", "ToolTip": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Utils\\ImageUtils.cs", "RelativeToolTip": "Utils\\ImageUtils.cs", "ViewState": "AgIAAIMAAAAAzczMzMw5wJEAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T13:14:03.208Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "ConditionalDiscriminator.cs", "DocumentMoniker": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Models\\ConditionalDiscriminator.cs", "RelativeDocumentMoniker": "Models\\ConditionalDiscriminator.cs", "ToolTip": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Models\\ConditionalDiscriminator.cs", "RelativeToolTip": "Models\\ConditionalDiscriminator.cs", "ViewState": "AgIAAEYAAAAAAAAAAIA4wFYAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T13:02:51.68Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "FloorPlanGANTrainer.cs", "DocumentMoniker": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Training\\FloorPlanGANTrainer.cs", "RelativeDocumentMoniker": "Training\\FloorPlanGANTrainer.cs", "ToolTip": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\Training\\FloorPlanGANTrainer.cs", "RelativeToolTip": "Training\\FloorPlanGANTrainer.cs", "ViewState": "AgIAABEAAAAAAAAAAAAowC8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T12:43:13.516Z", "EditorCaption": ""}]}]}]}