using FloorPlanGAN.Models;
using TorchSharp;
using static TorchSharp.torch;
using static TorchSharp.torch.nn;

namespace FloorPlanGAN
{
    /// <summary>
    /// 简化的测试程序，用于验证GAN框架是否正常工作
    /// </summary>
    public class TestProgram
    {
        public static void RunBasicTest()
        {
            Console.WriteLine("🧪 运行基础GAN测试...");
            
            try
            {
                // 强制使用CPU设备
                var device = torch.CPU;
                Console.WriteLine($"使用设备: {device}");
                
                // 创建基础配置
                var options = new FloorPlanGANOptions
                {
                    ImageSize = 64,  // 使用较小的图像尺寸进行测试
                    Channels = 3,
                    NoiseSize = 100,
                    GeneratorFeatures = 32,
                    DiscriminatorFeatures = 32,
                    Device = device
                };
                
                Console.WriteLine("✅ 配置创建成功");
                
                // 测试生成器
                TestGenerator(options);
                
                // 测试判别器
                TestDiscriminator(options);
                
                // 测试数据流
                TestDataFlow(options);
                
                Console.WriteLine("🎉 所有基础测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }
        
        private static void TestGenerator(FloorPlanGANOptions options)
        {
            Console.WriteLine("测试生成器...");
            
            var generator = new ConditionalGenerator(options).to(options.Device);
            generator.InitializeWeights();

            // 创建测试输入
            var batchSize = 2;
            var noise = torch.randn(batchSize, options.NoiseSize, 1, 1, device: options.Device);

            // 前向传播
            var generated = generator.forward(noise);
            
            Console.WriteLine($"生成器输出形状: [{string.Join(", ", generated.shape)}]");
            
            // 验证输出形状
            var expectedShape = new long[] { batchSize, options.Channels, options.ImageSize, options.ImageSize };
            if (generated.shape.SequenceEqual(expectedShape))
            {
                Console.WriteLine("✅ 生成器测试通过");
            }
            else
            {
                throw new Exception($"生成器输出形状错误，期望: [{string.Join(", ", expectedShape)}]，实际: [{string.Join(", ", generated.shape)}]");
            }
            
            // 清理
            noise.Dispose();
            generated.Dispose();
            generator.Dispose();
        }
        
        private static void TestDiscriminator(FloorPlanGANOptions options)
        {
            Console.WriteLine("测试判别器...");
            
            var discriminator = new ConditionalDiscriminator(options).to(options.Device);
            discriminator.InitializeWeights();

            // 创建测试输入
            var batchSize = 2;
            var image = torch.randn(batchSize, options.Channels, options.ImageSize, options.ImageSize, device: options.Device);

            // 前向传播
            var output = discriminator.forward(image);
            
            Console.WriteLine($"判别器输出形状: [{string.Join(", ", output.shape)}]");
            
            // 验证输出形状
            var expectedShape = new long[] { batchSize, 1 };
            if (output.shape.SequenceEqual(expectedShape))
            {
                Console.WriteLine("✅ 判别器测试通过");
            }
            else
            {
                throw new Exception($"判别器输出形状错误，期望: [{string.Join(", ", expectedShape)}]，实际: [{string.Join(", ", output.shape)}]");
            }
            
            // 清理
            image.Dispose();
            output.Dispose();
            discriminator.Dispose();
        }
        
        private static void TestDataFlow(FloorPlanGANOptions options)
        {
            Console.WriteLine("测试数据流...");
            
            // 创建模拟的训练步骤
            var generator = new ConditionalGenerator(options).to(options.Device);
            var discriminator = new ConditionalDiscriminator(options).to(options.Device);
            
            generator.InitializeWeights();
            discriminator.InitializeWeights();
            
            // 创建优化器
            var generatorOptimizer = optim.Adam(generator.parameters(), lr: 0.0002);
            var discriminatorOptimizer = optim.Adam(discriminator.parameters(), lr: 0.0002);
            
            var batchSize = 2;
            
            // 模拟一个训练步骤
            for (int step = 0; step < 3; step++)
            {
                // 创建模拟数据
                var realImages = torch.randn(batchSize, options.Channels, options.ImageSize, options.ImageSize, device: options.Device);
                var noise = torch.randn(batchSize, options.NoiseSize, 1, 1, device: options.Device);

                // 训练判别器
                discriminator.train();
                discriminatorOptimizer.zero_grad();

                var realOutput = discriminator.forward(realImages);
                var realLabels = torch.ones_like(realOutput);
                var realLoss = -(realLabels * torch.log(realOutput + 1e-8) + (1 - realLabels) * torch.log(1 - realOutput + 1e-8)).mean();

                var fakeImages = generator.forward(noise);
                var fakeOutput = discriminator.forward(fakeImages.detach());
                var fakeLabels = torch.zeros_like(fakeOutput);
                var fakeLoss = -(fakeLabels * torch.log(fakeOutput + 1e-8) + (1 - fakeLabels) * torch.log(1 - fakeOutput + 1e-8)).mean();

                var discriminatorLoss = realLoss + fakeLoss;
                discriminatorLoss.backward();
                discriminatorOptimizer.step();

                // 训练生成器
                generator.train();
                generatorOptimizer.zero_grad();

                var generatorOutput = discriminator.forward(fakeImages);
                var generatorLabels = torch.ones_like(generatorOutput);
                var generatorLoss = -(generatorLabels * torch.log(generatorOutput + 1e-8) + (1 - generatorLabels) * torch.log(1 - generatorOutput + 1e-8)).mean();
                generatorLoss.backward();
                generatorOptimizer.step();

                Console.WriteLine($"步骤 {step + 1}: D_Loss = {discriminatorLoss.item<float>():F4}, G_Loss = {generatorLoss.item<float>():F4}");

                // 清理
                realImages.Dispose();
                noise.Dispose();
                fakeImages.Dispose();
                realOutput.Dispose();
                fakeOutput.Dispose();
                generatorOutput.Dispose();
                realLoss.Dispose();
                fakeLoss.Dispose();
                discriminatorLoss.Dispose();
                generatorLoss.Dispose();
            }
            
            Console.WriteLine("✅ 数据流测试通过");
            
            // 清理
            generator.Dispose();
            discriminator.Dispose();
            generatorOptimizer.Dispose();
            discriminatorOptimizer.Dispose();
        }
    }
}
