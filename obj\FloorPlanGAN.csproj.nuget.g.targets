﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)torchsharp\0.102.7\buildTransitive\net6.0\TorchSharp.targets" Condition="Exists('$(NuGetPackageRoot)torchsharp\0.102.7\buildTransitive\net6.0\TorchSharp.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-primary.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-primary.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-fragment2\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-fragment2.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-fragment2\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-fragment2.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-fragment1.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part9-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part9-fragment1.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part8\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part8.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part8\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part8.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part7\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part7.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part7\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part7.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part6\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part6.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part6\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part6.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part5-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part5-primary.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part5-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part5-primary.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part5-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part5-fragment1.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part5-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part5-fragment1.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part4\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part4.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part4\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part4.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part3-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part3-primary.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part3-primary\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part3-primary.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part3-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part3-fragment1.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part3-fragment1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part3-fragment1.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part2\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part2.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part2\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part2.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part10\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part10.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part10\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part10.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part1.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64-part1\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64-part1.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cuda-12.1-win-x64\2.2.1.1\buildTransitive\netstandard2.0\libtorch-cuda-12.1-win-x64.targets')" />
    <Import Project="$(NuGetPackageRoot)torchsharp-cuda-windows\0.102.7\buildTransitive\net6.0\TorchSharp-cuda-windows.targets" Condition="Exists('$(NuGetPackageRoot)torchsharp-cuda-windows\0.102.7\buildTransitive\net6.0\TorchSharp-cuda-windows.targets')" />
  </ImportGroup>
</Project>