﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)torchsharp\0.101.5\buildTransitive\net6.0\TorchSharp.targets" Condition="Exists('$(NuGetPackageRoot)torchsharp\0.101.5\buildTransitive\net6.0\TorchSharp.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cpu-win-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-win-x64.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cpu-win-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-win-x64.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cpu-osx-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-osx-x64.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cpu-osx-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-osx-x64.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cpu-linux-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-linux-x64.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cpu-linux-x64\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu-linux-x64.targets')" />
    <Import Project="$(NuGetPackageRoot)libtorch-cpu\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu.targets" Condition="Exists('$(NuGetPackageRoot)libtorch-cpu\2.1.0.1\buildTransitive\netstandard2.0\libtorch-cpu.targets')" />
    <Import Project="$(NuGetPackageRoot)torchsharp-cpu\0.101.5\buildTransitive\net6.0\TorchSharp-cpu.targets" Condition="Exists('$(NuGetPackageRoot)torchsharp-cpu\0.101.5\buildTransitive\net6.0\TorchSharp-cpu.targets')" />
  </ImportGroup>
</Project>