using TorchSharp;
using TorchSharp.Modules;
using static TorchSharp.torch;
using static TorchSharp.torch.nn;

namespace FloorPlanGAN.Models
{
    /// <summary>
    /// 条件判别器 - 基于DCGAN架构
    /// 输入: 图像 (可以是真实平面图或生成的平面图)
    /// 输出: 真实性概率
    /// </summary>
    public class ConditionalDiscriminator : Module<Tensor, Tensor>
    {
        private readonly FloorPlanGANOptions _options;
        private readonly Sequential _main;

        public ConditionalDiscriminator(FloorPlanGANOptions options) : base(nameof(ConditionalDiscriminator))
        {
            _options = options;

            // 基于DCGAN的判别器架构
            _main = Sequential(
                // 输入: (nc) x 64 x 64
                ("conv1", Conv2d(options.Channels, options.DiscriminatorFeatures, 4, 2, 1, bias: false)),
                ("leaky_relu1", LeakyReLU(0.2, inplace: true)),

                // state size: (ndf) x 32 x 32
                ("conv2", Conv2d(options.DiscriminatorFeatures, options.DiscriminatorFeatures * 2, 4, 2, 1, bias: false)),
                ("bn2", BatchNorm2d(options.DiscriminatorFeatures * 2)),
                ("leaky_relu2", LeakyReLU(0.2, inplace: true)),

                // state size: (ndf*2) x 16 x 16
                ("conv3", Conv2d(options.DiscriminatorFeatures * 2, options.DiscriminatorFeatures * 4, 4, 2, 1, bias: false)),
                ("bn3", BatchNorm2d(options.DiscriminatorFeatures * 4)),
                ("leaky_relu3", LeakyReLU(0.2, inplace: true)),

                // state size: (ndf*4) x 8 x 8
                ("conv4", Conv2d(options.DiscriminatorFeatures * 4, options.DiscriminatorFeatures * 8, 4, 2, 1, bias: false)),
                ("bn4", BatchNorm2d(options.DiscriminatorFeatures * 8)),
                ("leaky_relu4", LeakyReLU(0.2, inplace: true)),

                // state size: (ndf*8) x 4 x 4
                ("conv5", Conv2d(options.DiscriminatorFeatures * 8, 1, 4, 1, 0, bias: false)),
                ("sigmoid", Sigmoid())
            );

            RegisterComponents();
        }

        public override Tensor forward(Tensor input)
        {
            var output = _main.forward(input);
            return output.view(-1, 1).squeeze(1);
        }

        /// <summary>
        /// 初始化权重 - 基于DCGAN论文的建议
        /// </summary>
        public void InitializeWeights()
        {
            this.apply(WeightsInit);
        }

        /// <summary>
        /// 权重初始化函数
        /// </summary>
        private static void WeightsInit(Module m)
        {
            var classname = m.GetType().Name;
            if (classname.Contains("Conv"))
            {
                if (m is Conv2d conv2d)
                {
                    init.normal_(conv2d.weight, 0.0, 0.02);
                }
            }
            else if (classname.Contains("BatchNorm"))
            {
                if (m is BatchNorm2d batchNorm2d)
                {
                    init.normal_(batchNorm2d.weight, 1.0, 0.02);
                    init.zeros_(batchNorm2d.bias);
                }
            }
        }
    }
}
