using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using TorchSharp;
using static TorchSharp.torch;
using static TorchSharp.torch.nn;


namespace FloorPlanGAN.Utils
{
    /// <summary>
    /// 图像处理工具类
    /// </summary>
    public static class ImageUtils
    {
        /// <summary>
        /// 将张量转换为图像
        /// </summary>
        public static Image<Rgb24> TensorToImage(Tensor tensor)
        {
            // 确保张量在CPU上
            tensor = tensor.cpu();
            
            // 处理批次维度
            if (tensor.dim() == 4)
            {
                tensor = tensor.select(0, 0); // 取第一个样本
            }
            
            if (tensor.dim() != 3 || tensor.shape[0] != 3)
            {
                throw new ArgumentException($"张量形状必须是 [3, H, W]，实际是 {string.Join(", ", tensor.shape)}");
            }
            
            var height = (int)tensor.shape[1];
            var width = (int)tensor.shape[2];
            
            // 反归一化：从 [-1, 1] 到 [0, 1]
            tensor = (tensor + 1.0f) / 2.0f;
            tensor = torch.clamp(tensor, 0.0f, 1.0f);
            
            var image = new Image<Rgb24>(width, height);
            
            // 获取张量数据
            var data = tensor.data<float>().ToArray();
            
            image.ProcessPixelRows(accessor =>
            {
                for (int y = 0; y < height; y++)
                {
                    var row = accessor.GetRowSpan(y);
                    for (int x = 0; x < width; x++)
                    {
                        var baseIndex = y * width + x;
                        
                        var r = (byte)(data[baseIndex] * 255);
                        var g = (byte)(data[height * width + baseIndex] * 255);
                        var b = (byte)(data[2 * height * width + baseIndex] * 255);
                        
                        row[x] = new Rgb24(r, g, b);
                    }
                }
            });
            
            return image;
        }

        /// <summary>
        /// 将图像转换为张量
        /// </summary>
        public static Tensor ImageToTensor(Image<Rgb24> image)
        {
            var width = image.Width;
            var height = image.Height;
            var data = new float[3 * height * width];

            image.ProcessPixelRows(accessor =>
            {
                for (int y = 0; y < height; y++)
                {
                    var row = accessor.GetRowSpan(y);
                    for (int x = 0; x < width; x++)
                    {
                        var pixel = row[x];
                        var baseIndex = y * width + x;
                        
                        data[baseIndex] = pixel.R / 255.0f;                    // R通道
                        data[height * width + baseIndex] = pixel.G / 255.0f;   // G通道
                        data[2 * height * width + baseIndex] = pixel.B / 255.0f; // B通道
                    }
                }
            });

            var tensor = torch.tensor(data).reshape(3, height, width);
            
            // 归一化到 [-1, 1]
            tensor = tensor * 2.0f - 1.0f;
            
            return tensor;
        }

        /// <summary>
        /// 保存对比网格图像
        /// </summary>
        public static async Task SaveComparisonGridAsync(Tensor outlines, Tensor realFloorplans, Tensor generatedFloorplans, string outputPath)
        {
            var batchSize = (int)outlines.shape[0];
            var imageSize = (int)outlines.shape[2];
            // 限制显示的样本数量
            var displayCount = Math.Min(batchSize, 8);
            // 创建网格图像 (3列：外轮廓、真实平面图、生成平面图)
            var gridWidth = 3 * imageSize + 2 * 10; // 3张图片 + 2个间隔
            var gridHeight = displayCount * imageSize + (displayCount - 1) * 10; // displayCount行 + 间隔
            var gridImage = new Image<Rgb24>(gridWidth, gridHeight);
            // 填充白色背景
            gridImage.Mutate(x => x.BackgroundColor(Color.White));
            for (int i = 0; i < displayCount; i++)
            {
                // 转换张量为图像
                var outlineImage = TensorToImage(outlines.select(0, i));
                var realImage = TensorToImage(realFloorplans.select(0, i));
                var generatedImage = TensorToImage(generatedFloorplans.select(0, i));
                // 调整图像大小
                outlineImage.Mutate(x => x.Resize(imageSize, imageSize));
                realImage.Mutate(x => x.Resize(imageSize, imageSize));
                generatedImage.Mutate(x => x.Resize(imageSize, imageSize));
                // 计算位置
                var y = i * (imageSize + 10);
                // 绘制到网格
                gridImage.Mutate(x =>
                {
                    x.DrawImage(outlineImage, new Point(0, y), 1.0f);
                    x.DrawImage(realImage, new Point(imageSize + 10, y), 1.0f);
                    x.DrawImage(generatedImage, new Point(2 * (imageSize + 10), y), 1.0f);
                });
                
                // 释放临时图像
                outlineImage.Dispose();
                realImage.Dispose();
                generatedImage.Dispose();
            }
            
            // 保存网格图像
            await gridImage.SaveAsPngAsync(outputPath);
            gridImage.Dispose();
        }

        /// <summary>
        /// 保存单个图像
        /// </summary>
        public static async Task SaveImageAsync(Tensor tensor, string outputPath)
        {
            using var image = TensorToImage(tensor);
            await image.SaveAsPngAsync(outputPath);
        }

        /// <summary>
        /// 创建训练进度可视化
        /// </summary>
        public static async Task SaveTrainingProgressAsync(List<double> generatorLosses, List<double> discriminatorLosses, string outputPath)
        {
            // 这里可以使用图表库来生成损失曲线图
            // 由于简化，我们只保存CSV数据
            var csvPath = Path.ChangeExtension(outputPath, ".csv");
            using var writer = new StreamWriter(csvPath);
            writer.WriteLine("Epoch,GeneratorLoss,DiscriminatorLoss");
            
            for (int i = 0; i < generatorLosses.Count; i++)
            {
                writer.WriteLine($"{i + 1},{generatorLosses[i]:F6},{discriminatorLosses[i]:F6}");
            }
            
            Console.WriteLine($"训练进度数据已保存: {csvPath}");
        }

        /// <summary>
        /// 计算图像质量指标
        /// </summary>
        public static Dictionary<string, double> CalculateImageMetrics(Tensor real, Tensor generated)
        {
            var metrics = new Dictionary<string, double>();
            
            // 确保张量在CPU上
            real = real.cpu();
            generated = generated.cpu();
            
            // PSNR (峰值信噪比)
            var mse = functional.mse_loss(real, generated).item<double>();
            var psnr = mse > 0 ? 20 * Math.Log10(1.0 / Math.Sqrt(mse)) : double.PositiveInfinity;
            metrics["PSNR"] = psnr;
            
            // MAE (平均绝对误差)
            var mae = functional.l1_loss(real, generated).item<double>();
            metrics["MAE"] = mae;
            
            // MSE (均方误差)
            metrics["MSE"] = mse;
            
            return metrics;
        }

        /// <summary>
        /// 应用图像后处理
        /// </summary>
        public static Tensor PostProcessImage(Tensor tensor)
        {
            // 确保值在有效范围内
            tensor = torch.clamp(tensor, -1.0f, 1.0f);
            
            // 可以添加其他后处理步骤，如：
            // - 去噪
            // - 锐化
            // - 对比度增强
            
            return tensor;
        }

        /// <summary>
        /// 创建图像马赛克
        /// </summary>
        public static async Task SaveImageMosaicAsync(List<Tensor> images, string outputPath, 
                                                     int gridCols = 4, int imageSize = 256)
        {
            var imageCount = images.Count;
            var gridRows = (int)Math.Ceiling((double)imageCount / gridCols);
            
            var mosaicWidth = gridCols * imageSize + (gridCols - 1) * 5;
            var mosaicHeight = gridRows * imageSize + (gridRows - 1) * 5;
            
            var mosaicImage = new Image<Rgb24>(mosaicWidth, mosaicHeight);
            mosaicImage.Mutate(x => x.BackgroundColor(Color.White));
            
            for (int i = 0; i < imageCount; i++)
            {
                var row = i / gridCols;
                var col = i % gridCols;
                
                var x = col * (imageSize + 5);
                var y = row * (imageSize + 5);
                
                using var image = TensorToImage(images[i]);
                image.Mutate(x => x.Resize(imageSize, imageSize));
                
                mosaicImage.Mutate(m => m.DrawImage(image, new Point(x, y), 1.0f));
            }
            
            await mosaicImage.SaveAsPngAsync(outputPath);
            mosaicImage.Dispose();
        }

        /// <summary>
        /// 验证图像张量格式
        /// </summary>
        public static bool ValidateImageTensor(Tensor tensor)
        {
            // 检查维度
            if (tensor.dim() != 3 && tensor.dim() != 4)
            {
                return false;
            }
            
            // 检查通道数
            var channels = tensor.dim() == 4 ? tensor.shape[1] : tensor.shape[0];
            if (channels != 1 && channels != 3)
            {
                return false;
            }
            
            // 检查数值范围
            var minVal = tensor.min().item<float>();
            var maxVal = tensor.max().item<float>();
            
            // 应该在 [-1, 1] 或 [0, 1] 范围内
            if ((minVal >= -1.1f && maxVal <= 1.1f) || (minVal >= -0.1f && maxVal <= 1.1f))
            {
                return true;
            }
            
            return false;
        }
    }
}
