using FloorPlanGAN.Data;
using FloorPlanGAN.Models;
using FloorPlanGAN.Utils;
using TorchSharp;
using TorchSharp.Modules;
using static TorchSharp.torch;
using static TorchSharp.torch.nn;

namespace FloorPlanGAN.Training
{
    /// <summary>
    /// 建筑平面图GAN训练器
    /// </summary>
    public class FloorPlanGANTrainer
    {
        private readonly FloorPlanGANOptions _options;
        private readonly ConditionalGenerator _generator;
        private readonly ConditionalDiscriminator _discriminator;
        private readonly FloorPlanDataset _dataset;
        private readonly optim.Optimizer _generatorOptimizer;
        private readonly optim.Optimizer _discriminatorOptimizer;
        private readonly LossCalculator _lossCalculator;

        // 训练统计
        private readonly List<double> _generatorLosses = new();
        private readonly List<double> _discriminatorLosses = new();
        private readonly List<double> _realAccuracies = new();
        private readonly List<double> _fakeAccuracies = new();

        public FloorPlanGANTrainer(FloorPlanGANOptions options)
        {
            _options = options;

            // 设置随机种子
            torch.manual_seed(_options.RandomSeed);
            if (torch.cuda.is_available())
            {
                torch.cuda.manual_seed(_options.RandomSeed);
            }

            // 创建模型
            _generator = new ConditionalGenerator(_options).to(_options.Device);
            _discriminator = new ConditionalDiscriminator(_options).to(_options.Device);

            // 初始化权重
            _generator.InitializeWeights();
            _discriminator.InitializeWeights();

            // 创建优化器
            _generatorOptimizer = optim.Adam(
                _generator.parameters(),
                lr: _options.LearningRate,
                beta1: _options.Beta1,
                beta2: _options.Beta2
            );

            _discriminatorOptimizer = optim.Adam(
                _discriminator.parameters(),
                lr: _options.LearningRate,
                beta1: _options.Beta1,
                beta2: _options.Beta2
            );

            // 创建数据集
            _dataset = new FloorPlanDataset(_options.DataRoot, _options.ImageSize);
            _dataset.ValidateDataset();

            // 创建损失计算器
            _lossCalculator = new LossCalculator(_options.LossType);

            // 创建输出目录
            Directory.CreateDirectory(_options.CheckpointDir);
            Directory.CreateDirectory(_options.OutputDir);

            Console.WriteLine($"训练器初始化完成:");
            Console.WriteLine($"  设备: {_options.Device}");
            Console.WriteLine($"  数据集大小: {_dataset.Count}");
            Console.WriteLine($"  批次大小: {_options.BatchSize}");
            Console.WriteLine($"  训练轮数: {_options.NumEpochs}");
        }

        /// <summary>
        /// 开始训练
        /// </summary>
        public async Task TrainAsync()
        {
            Console.WriteLine("开始训练...");
            
            var startTime = DateTime.Now;
            var totalBatches = (_dataset.Count + _options.BatchSize - 1) / _options.BatchSize;

            for (int epoch = 0; epoch < _options.NumEpochs; epoch++)
            {
                var epochStartTime = DateTime.Now;
                var epochGeneratorLoss = 0.0;
                var epochDiscriminatorLoss = 0.0;
                var epochRealAccuracy = 0.0;
                var epochFakeAccuracy = 0.0;
                var batchCount = 0;

                Console.WriteLine($"\nEpoch {epoch + 1}/{_options.NumEpochs}");

                // 创建数据加载器
                var dataLoader = _dataset.CreateDataLoader(_options.BatchSize, shuffle: true);

                foreach (var (outlines, floorplans) in dataLoader)
                {
                    var batchStartTime = DateTime.Now;

                    // 移动到设备 - 只使用平面图作为真实图像
                    var realImages = floorplans.to(_options.Device);
                    var batchSize = realImages.shape[0];

                    // 训练判别器
                    var (dLoss, realAcc, fakeAcc) = TrainDiscriminator(realImages);

                    // 训练生成器
                    var gLoss = TrainGenerator();

                    // 更新统计
                    epochGeneratorLoss += gLoss;
                    epochDiscriminatorLoss += dLoss;
                    epochRealAccuracy += realAcc;
                    epochFakeAccuracy += fakeAcc;
                    batchCount++;

                    // 记录日志
                    if (batchCount % _options.LogInterval == 0)
                    {
                        var batchTime = DateTime.Now - batchStartTime;
                        Console.WriteLine($"  Batch {batchCount}/{totalBatches} - " +
                                        $"G_Loss: {gLoss:F4}, D_Loss: {dLoss:F4}, " +
                                        $"Real_Acc: {realAcc:F3}, Fake_Acc: {fakeAcc:F3}, " +
                                        $"Time: {batchTime.TotalSeconds:F1}s");
                    }

                    // 释放内存
                    realImages.Dispose();
                }

                // 计算平均损失
                epochGeneratorLoss /= batchCount;
                epochDiscriminatorLoss /= batchCount;
                epochRealAccuracy /= batchCount;
                epochFakeAccuracy /= batchCount;

                // 记录统计
                _generatorLosses.Add(epochGeneratorLoss);
                _discriminatorLosses.Add(epochDiscriminatorLoss);
                _realAccuracies.Add(epochRealAccuracy);
                _fakeAccuracies.Add(epochFakeAccuracy);

                var epochTime = DateTime.Now - epochStartTime;
                Console.WriteLine($"Epoch {epoch + 1} 完成 - " +
                                $"G_Loss: {epochGeneratorLoss:F4}, D_Loss: {epochDiscriminatorLoss:F4}, " +
                                $"Real_Acc: {epochRealAccuracy:F3}, Fake_Acc: {epochFakeAccuracy:F3}, " +
                                $"Time: {epochTime.TotalMinutes:F1}min");

                // 生成样本
                if ((epoch + 1) % _options.SampleInterval == 0)
                {
                    await GenerateSamplesAsync(epoch + 1);
                }

                // 保存模型
                if ((epoch + 1) % _options.SaveInterval == 0)
                {
                    SaveCheckpoint(epoch + 1);
                }
            }

            var totalTime = DateTime.Now - startTime;
            Console.WriteLine($"\n训练完成! 总用时: {totalTime.TotalHours:F1} 小时");
            
            // 保存最终模型
            SaveCheckpoint(_options.NumEpochs, "final");
            
            // 保存训练统计
            SaveTrainingStats();
        }

        /// <summary>
        /// 训练判别器
        /// </summary>
        private (double loss, double realAccuracy, double fakeAccuracy) TrainDiscriminator(Tensor realImages)
        {
            _discriminator.train();
            _discriminatorOptimizer.zero_grad();

            var batchSize = realImages.shape[0];

            // 训练真实样本
            var realOutput = _discriminator.forward(realImages);
            var realLabels = torch.ones_like(realOutput) * (1.0f - (float)_options.LabelSmoothing);
            var realLoss = -(realLabels * torch.log(realOutput + 1e-8) + (1 - realLabels) * torch.log(1 - realOutput + 1e-8)).mean();

            // 生成假样本
            var noise = torch.randn(batchSize, _options.NoiseSize, 1, 1, device: _options.Device);
            using var _ = torch.no_grad();
            var fakeImages = _generator.forward(noise);

            var fakeOutput = _discriminator.forward(fakeImages.detach());
            var fakeLabels = torch.zeros_like(fakeOutput);
            var fakeLoss = -(fakeLabels * torch.log(fakeOutput + 1e-8) + (1 - fakeLabels) * torch.log(1 - fakeOutput + 1e-8)).mean();

            var totalLoss = realLoss + fakeLoss;
            totalLoss.backward();
            _discriminatorOptimizer.step();

            // 计算准确率
            using var __ = torch.no_grad();
            var realAccuracy = torch.gt(realOutput, 0.5).to_type(ScalarType.Float32).mean().item<float>();
            var fakeAccuracy = torch.le(fakeOutput, 0.5).to_type(ScalarType.Float32).mean().item<float>();

            fakeImages.Dispose();
            noise.Dispose();

            return (totalLoss.item<double>(), realAccuracy, fakeAccuracy);
        }

        /// <summary>
        /// 训练生成器
        /// </summary>
        private double TrainGenerator()
        {
            _generator.train();
            _generatorOptimizer.zero_grad();

            var batchSize = _options.BatchSize;

            // 生成假样本
            var noise = torch.randn(batchSize, _options.NoiseSize, 1, 1, device: _options.Device);
            var fakeImages = _generator.forward(noise);

            // 判别器评估
            var fakeOutput = _discriminator.forward(fakeImages);
            var fakeLabels = torch.ones_like(fakeOutput); // 生成器希望判别器认为是真的

            var generatorLoss = -(fakeLabels * torch.log(fakeOutput + 1e-8) + (1 - fakeLabels) * torch.log(1 - fakeOutput + 1e-8)).mean();
            generatorLoss.backward();
            _generatorOptimizer.step();

            noise.Dispose();
            fakeImages.Dispose();

            return generatorLoss.item<double>();
        }

        /// <summary>
        /// 生成样本
        /// </summary>
        private async Task GenerateSamplesAsync(int epoch)
        {
            _generator.eval();

            using var _ = torch.no_grad();

            // 生成固定噪声的样本
            var fixedNoise = torch.randn(8, _options.NoiseSize, 1, 1, device: _options.Device);
            var generatedImages = _generator.forward(fixedNoise);

            // 保存样本 - 只保存第一个图像
            var outputPath = Path.Combine(_options.OutputDir, $"epoch_{epoch:D3}.png");
            await ImageUtils.SaveImageAsync(generatedImages.select(0, 0), outputPath);

            Console.WriteLine($"样本已保存: {outputPath}");

            // 释放内存
            generatedImages.Dispose();
            fixedNoise.Dispose();
        }

        /// <summary>
        /// 保存检查点
        /// </summary>
        private void SaveCheckpoint(int epoch, string suffix = "")
        {
            var checkpointPath = Path.Combine(_options.CheckpointDir,
                string.IsNullOrEmpty(suffix) ? $"checkpoint_epoch_{epoch:D3}.pth" : $"checkpoint_{suffix}.pth");

            try
            {
                // 保存生成器和判别器
                _generator.save(Path.Combine(_options.CheckpointDir, $"generator_epoch_{epoch:D3}.pth"));
                _discriminator.save(Path.Combine(_options.CheckpointDir, $"discriminator_epoch_{epoch:D3}.pth"));

                // 保存训练统计
                var statsPath = Path.Combine(_options.CheckpointDir, $"stats_epoch_{epoch:D3}.json");
                var stats = new
                {
                    epoch = epoch,
                    generator_losses = _generatorLosses.ToArray(),
                    discriminator_losses = _discriminatorLosses.ToArray(),
                    real_accuracies = _realAccuracies.ToArray(),
                    fake_accuracies = _fakeAccuracies.ToArray()
                };

                File.WriteAllText(statsPath, System.Text.Json.JsonSerializer.Serialize(stats));
                Console.WriteLine($"检查点已保存: {checkpointPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存检查点失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载检查点
        /// </summary>
        public void LoadCheckpoint(string generatorPath, string discriminatorPath)
        {
            if (!File.Exists(generatorPath))
            {
                throw new FileNotFoundException($"生成器文件不存在: {generatorPath}");
            }

            if (!File.Exists(discriminatorPath))
            {
                throw new FileNotFoundException($"判别器文件不存在: {discriminatorPath}");
            }

            try
            {
                _generator.load(generatorPath);
                _discriminator.load(discriminatorPath);
                Console.WriteLine($"模型已加载: {generatorPath}, {discriminatorPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载模型失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 加载检查点 (重载方法，自动查找配对文件)
        /// </summary>
        public void LoadCheckpoint(string checkpointDir, int epoch)
        {
            var generatorPath = Path.Combine(checkpointDir, $"generator_epoch_{epoch:D3}.pth");
            var discriminatorPath = Path.Combine(checkpointDir, $"discriminator_epoch_{epoch:D3}.pth");
            LoadCheckpoint(generatorPath, discriminatorPath);
        }

        /// <summary>
        /// 保存训练统计
        /// </summary>
        private void SaveTrainingStats()
        {
            var statsPath = Path.Combine(_options.OutputDir, "training_stats.csv");

            using var writer = new StreamWriter(statsPath);
            writer.WriteLine("Epoch,GeneratorLoss,DiscriminatorLoss,RealAccuracy,FakeAccuracy");

            for (int i = 0; i < _generatorLosses.Count; i++)
            {
                writer.WriteLine($"{i + 1},{_generatorLosses[i]:F6},{_discriminatorLosses[i]:F6}," +
                               $"{_realAccuracies[i]:F6},{_fakeAccuracies[i]:F6}");
            }

            Console.WriteLine($"训练统计已保存: {statsPath}");
        }

        /// <summary>
        /// 生成单个样本 (推理)
        /// </summary>
        public Tensor GenerateSample(Tensor? noise = null)
        {
            _generator.eval();

            using var _ = torch.no_grad();

            if ((bool)(noise == null))
            {
                noise = torch.randn(1, _options.NoiseSize, 1, 1, device: _options.Device);
            }
            else
            {
                noise = noise.to(_options.Device);
            }

            var generated = _generator.forward(noise);

            return generated.cpu();
        }

        /// <summary>
        /// 评估模型
        /// </summary>
        public async Task<Dictionary<string, double>> EvaluateAsync()
        {
            _generator.eval();
            _discriminator.eval();

            var metrics = new Dictionary<string, double>();
            var totalSamples = Math.Min(100, _dataset.Count);

            using var _ = torch.no_grad();

            var totalGeneratorLoss = 0.0;
            var totalDiscriminatorLoss = 0.0;
            var totalRealAccuracy = 0.0;
            var totalFakeAccuracy = 0.0;
            var batchCount = 0;

            var dataLoader = _dataset.CreateDataLoader(_options.BatchSize, shuffle: false);

            foreach (var (outlines, floorplans) in dataLoader.Take(totalSamples / _options.BatchSize))
            {
                var realImages = floorplans.to(_options.Device);
                var batchSize = realImages.shape[0];

                // 生成假样本
                var noise = torch.randn(batchSize, _options.NoiseSize, 1, 1, device: _options.Device);
                var fakeImages = _generator.forward(noise);

                // 判别器评估
                var realOutput = _discriminator.forward(realImages);
                var fakeOutput = _discriminator.forward(fakeImages);

                // 计算损失
                var realLabels = torch.ones_like(realOutput);
                var fakeLabels = torch.zeros_like(fakeOutput);

                var realLoss = -(realLabels * torch.log(realOutput + 1e-8) + (1 - realLabels) * torch.log(1 - realOutput + 1e-8)).mean();
                var fakeLoss = -(fakeLabels * torch.log(fakeOutput + 1e-8) + (1 - fakeLabels) * torch.log(1 - fakeOutput + 1e-8)).mean();
                var discriminatorLoss = realLoss + fakeLoss;

                var generatorLabels = torch.ones_like(fakeOutput);
                var generatorLoss = -(generatorLabels * torch.log(fakeOutput + 1e-8) + (1 - generatorLabels) * torch.log(1 - fakeOutput + 1e-8)).mean();

                // 计算准确率
                var realAccuracy = torch.gt(realOutput, 0.5).to_type(ScalarType.Float32).mean().item<float>();
                var fakeAccuracy = torch.le(fakeOutput, 0.5).to_type(ScalarType.Float32).mean().item<float>();

                totalGeneratorLoss += generatorLoss.item<double>();
                totalDiscriminatorLoss += discriminatorLoss.item<double>();
                totalRealAccuracy += realAccuracy;
                totalFakeAccuracy += fakeAccuracy;
                batchCount++;

                // 释放内存
                realImages.Dispose();
                fakeImages.Dispose();
                noise.Dispose();
            }

            metrics["generator_loss"] = totalGeneratorLoss / batchCount;
            metrics["discriminator_loss"] = totalDiscriminatorLoss / batchCount;
            metrics["real_accuracy"] = totalRealAccuracy / batchCount;
            metrics["fake_accuracy"] = totalFakeAccuracy / batchCount;

            return metrics;
        }
    }
}
