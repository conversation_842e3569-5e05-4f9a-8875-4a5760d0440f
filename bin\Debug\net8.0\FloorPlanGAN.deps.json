{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"FloorPlanGAN/1.0.0": {"dependencies": {"SixLabors.ImageSharp": "3.1.11", "System.Drawing.Common": "8.0.8", "TorchSharp": "0.102.7", "TorchSharp-cuda-windows": "0.102.7"}, "runtime": {"FloorPlanGAN.dll": {}}}, "Google.Protobuf/3.21.9": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.21.9.0", "fileVersion": "3.21.9.0"}}}, "libtorch-cuda-12.1-win-x64/*******": {"dependencies": {"libtorch-cuda-12.1-win-x64-part1": "*******", "libtorch-cuda-12.1-win-x64-part10": "*******", "libtorch-cuda-12.1-win-x64-part2": "*******", "libtorch-cuda-12.1-win-x64-part3-fragment1": "*******", "libtorch-cuda-12.1-win-x64-part3-primary": "*******", "libtorch-cuda-12.1-win-x64-part4": "*******", "libtorch-cuda-12.1-win-x64-part5-fragment1": "*******", "libtorch-cuda-12.1-win-x64-part5-primary": "*******", "libtorch-cuda-12.1-win-x64-part6": "*******", "libtorch-cuda-12.1-win-x64-part7": "*******", "libtorch-cuda-12.1-win-x64-part8": "*******", "libtorch-cuda-12.1-win-x64-part9-fragment1": "*******", "libtorch-cuda-12.1-win-x64-part9-fragment2": "*******", "libtorch-cuda-12.1-win-x64-part9-primary": "*******"}}, "libtorch-cuda-12.1-win-x64-part1/*******": {"runtimeTargets": {"runtimes/win-x64/native/asmjit.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/c10.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/c10_cuda.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/caffe2_nvrtc.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/cublas64_12.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.1213"}, "runtimes/win-x64/native/cudart64_12.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.12010"}, "runtimes/win-x64/native/cudnn64_8.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.6050"}, "runtimes/win-x64/native/nvJitLink_120_0.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.9000"}, "runtimes/win-x64/native/nvrtc64_120_0.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.9000"}, "runtimes/win-x64/native/torch_global_deps.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/uv.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/zlibwapi.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.2.3.0"}}}, "libtorch-cuda-12.1-win-x64-part10/*******": {"runtimeTargets": {"runtimes/win-x64/native/cufft64_11.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.1102"}, "runtimes/win-x64/native/cufftw64_11.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.1102"}, "runtimes/win-x64/native/cupti64_2023.1.1.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2023.1.1.0"}, "runtimes/win-x64/native/curand64_10.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.1032"}}}, "libtorch-cuda-12.1-win-x64-part2/*******": {"runtimeTargets": {"runtimes/win-x64/native/cudnn_adv_infer64_8.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.12000"}, "runtimes/win-x64/native/cudnn_adv_train64_8.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.12000"}}}, "libtorch-cuda-12.1-win-x64-part3-fragment1/*******": {}, "libtorch-cuda-12.1-win-x64-part3-primary/*******": {"runtimeTargets": {"runtimes/win-x64/native/cudnn_cnn_infer64_8.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.12000"}, "runtimes/win-x64/native/cudnn_cnn_infer64_8.dll.sha": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "libtorch-cuda-12.1-win-x64-part4/*******": {"runtimeTargets": {"runtimes/win-x64/native/cudnn_cnn_train64_8.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.12000"}, "runtimes/win-x64/native/cudnn_ops_infer64_8.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.12000"}, "runtimes/win-x64/native/cudnn_ops_train64_8.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.12000"}}}, "libtorch-cuda-12.1-win-x64-part5-fragment1/*******": {}, "libtorch-cuda-12.1-win-x64-part5-primary/*******": {"runtimeTargets": {"runtimes/win-x64/native/cublasLt64_12.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.1213"}, "runtimes/win-x64/native/cublasLt64_12.dll.sha": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "libtorch-cuda-12.1-win-x64-part6/*******": {"runtimeTargets": {"runtimes/win-x64/native/cusolver64_11.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.1145"}, "runtimes/win-x64/native/cusolverMg64_11.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.1145"}, "runtimes/win-x64/native/fbgemm.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/fbjni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libiomp5md.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.0.2023.118"}, "runtimes/win-x64/native/libiompstubs5md.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.0.2023.118"}, "runtimes/win-x64/native/nvToolsExt64_1.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/nvrtc-builtins64_121.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "libtorch-cuda-12.1-win-x64-part7/*******": {"runtimeTargets": {"runtimes/win-x64/native/pytorch_jni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/torch.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/torch_cpu.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "libtorch-cuda-12.1-win-x64-part8/*******": {"runtimeTargets": {"runtimes/win-x64/native/cusparse64_12.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.14.11.1210"}}}, "libtorch-cuda-12.1-win-x64-part9-fragment1/*******": {}, "libtorch-cuda-12.1-win-x64-part9-fragment2/*******": {}, "libtorch-cuda-12.1-win-x64-part9-primary/*******": {"runtimeTargets": {"runtimes/win-x64/native/torch_cuda.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/torch_cuda.dll.sha": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "SharpZipLib/1.4.0": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.ImageSharp/3.1.11": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "SkiaSharp/2.88.6": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.6", "SkiaSharp.NativeAssets.macOS": "2.88.6"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.Drawing.Common/8.0.8": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}}}, "System.Memory/4.5.5": {}, "TorchSharp/0.102.7": {"dependencies": {"Google.Protobuf": "3.21.9", "SharpZipLib": "1.4.0", "SkiaSharp": "2.88.6", "System.Memory": "4.5.5"}, "runtime": {"lib/net6.0/TorchSharp.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "runtimeTargets": {"runtimes/linux-x64/native/libLibTorchSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libLibTorchSharp.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libLibTorchSharp.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/LibTorchSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "TorchSharp-cuda-windows/0.102.7": {"dependencies": {"TorchSharp": "0.102.7", "libtorch-cuda-12.1-win-x64": "*******"}}}}, "libraries": {"FloorPlanGAN/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Google.Protobuf/3.21.9": {"type": "package", "serviceable": true, "sha512": "sha512-OTpFujTgkmqMLbg3KT7F/iuKi1rg6s5FCS2M9XcVLDn40zL8wgXm37CY/F6MeOEXKjdcnXGCN/h7oyMkVydVsg==", "path": "google.protobuf/3.21.9", "hashPath": "google.protobuf.3.21.9.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64/*******": {"type": "package", "serviceable": true, "sha512": "sha512-fSFsCwBtQqoLV5Ede1fsymQHU1Fw50JN5A+29hu+IHuTOskFLM7AUUFTm8ljlyVghlC8CjsuYhjFSQoyt8PGXQ==", "path": "libtorch-cuda-12.1-win-x64/*******", "hashPath": "libtorch-cuda-12.1-win-x64.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part1/*******": {"type": "package", "serviceable": true, "sha512": "sha512-FJuypx3MUPbbMWDqj0vxAcQs7Xs9GK2D1ed1BeqN10lt7MpsTyXUORg8pxokzbbjEBlnf0wC8d25JN1oL46l4w==", "path": "libtorch-cuda-12.1-win-x64-part1/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part1.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part10/*******": {"type": "package", "serviceable": true, "sha512": "sha512-Pl/4S1P5Axgs7VAsdIm+zXwyd238zsKqOHnL6alNGF6UmjW4scSfLvemzQ80iK08VSqUOo2Zf2PDRD4T2iAyoA==", "path": "libtorch-cuda-12.1-win-x64-part10/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part10.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part2/*******": {"type": "package", "serviceable": true, "sha512": "sha512-OcnU8im9yaNVY7aaCLPw8olPNsjW/WEwR8h2ZY/qLgGsXBtGxzfan+2xewgt+JnBclmaNdCZjKTNlQl+53ZxFQ==", "path": "libtorch-cuda-12.1-win-x64-part2/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part2.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part3-fragment1/*******": {"type": "package", "serviceable": true, "sha512": "sha512-AJbZauBUa/H40PdfMRPURtgAK/UAXoC15zsYW0Rh1tN6U5LRny/LzU+B9rOCHFajHwgU/E/WjVqWJhybR5dumw==", "path": "libtorch-cuda-12.1-win-x64-part3-fragment1/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part3-fragment1.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part3-primary/*******": {"type": "package", "serviceable": true, "sha512": "sha512-FZFdU5C1CgfJXy/Z07aVJzxF0so3c6MBLiibP4x+6GbXUt/gdZAQPfAQDs3U5eLX0R7OcIVBZcz+ib4QjoA+cA==", "path": "libtorch-cuda-12.1-win-x64-part3-primary/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part3-primary.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part4/*******": {"type": "package", "serviceable": true, "sha512": "sha512-AvVKciGKmsKGXD+hEYe+nrBSjWdGT1XIILEHAPswyJtVscER1aoz+CySYxhkaWIWrsI354W7nUZwxcCiKu3dWQ==", "path": "libtorch-cuda-12.1-win-x64-part4/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part4.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part5-fragment1/*******": {"type": "package", "serviceable": true, "sha512": "sha512-x6MF6VfuqXiPhfAKld91fSnaX3V/p9MWTIvMbmzyC7MIpF+uCvKMCuVEhgC0aeIlFEkPCNq7KAY9zxYPrWVLEw==", "path": "libtorch-cuda-12.1-win-x64-part5-fragment1/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part5-fragment1.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part5-primary/*******": {"type": "package", "serviceable": true, "sha512": "sha512-qdOSsyO6F+XLhS6OMd/oMeQc0s41NUKeluMelyX5+bj6btOnQy/KshWdUqDI+KpjY2zG/FOwNKBMNjf3/KFT5Q==", "path": "libtorch-cuda-12.1-win-x64-part5-primary/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part5-primary.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part6/*******": {"type": "package", "serviceable": true, "sha512": "sha512-08Bsxlwp2I7K0oKgwDUC+Y6M55Nge8Pa7z9/gvRHrM5nv4stSWUN3V7t8WVeZfiSqj0iXeLNlP3eynt6qBiRzg==", "path": "libtorch-cuda-12.1-win-x64-part6/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part6.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part7/*******": {"type": "package", "serviceable": true, "sha512": "sha512-vBLDVpx5wgCoqSV6ncYTFzJkVwr5/iWJC+y1HliLy5270iOD/lQ2WHNBbqy8YciaRYRYLKOQEha/yMJG542b4g==", "path": "libtorch-cuda-12.1-win-x64-part7/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part7.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part8/*******": {"type": "package", "serviceable": true, "sha512": "sha512-WTS+eU2qW4B3mkW8dUwisj1wBzg0krMD0fV+Ryq9a6eBqsT2y/NKTL4+567unvwxDaAqJBfC7nLNgVBwLcNgGA==", "path": "libtorch-cuda-12.1-win-x64-part8/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part8.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part9-fragment1/*******": {"type": "package", "serviceable": true, "sha512": "sha512-ij3t/I+kkVrBFSuB1/S3lNKblHeiLLlyhrgoRwu2m8mAPEJoioHO5H/4sSM0UDp3YRYp31EAlK1e+pkHsCTZiw==", "path": "libtorch-cuda-12.1-win-x64-part9-fragment1/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part9-fragment1.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part9-fragment2/*******": {"type": "package", "serviceable": true, "sha512": "sha512-cOqLuqdig1+Ra6REXB44Hmou7xL2ZYESxPrTS0MFocwUz1h31Q4YVw4a0YUQpWf/rx8vh26Gb18jRW02/wUlxA==", "path": "libtorch-cuda-12.1-win-x64-part9-fragment2/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part9-fragment2.*******.nupkg.sha512"}, "libtorch-cuda-12.1-win-x64-part9-primary/*******": {"type": "package", "serviceable": true, "sha512": "sha512-cgI/G0BZeAoXmYtzOqaSApCDse5iYFk5WPqCcq8vwLV3R2Ag5D5eMKz5eKgjD5d0etJmJzloyYFixXp2rHpC7Q==", "path": "libtorch-cuda-12.1-win-x64-part9-primary/*******", "hashPath": "libtorch-cuda-12.1-win-x64-part9-primary.*******.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "SharpZipLib/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-CdkbBSPIpHD8xBlu+8kDJiqc1Tf9iV89BObnqcvEbwysXSj5h1MfaeLgeeaxPZmi7CTJO8FDofBBNxBW0Vml7A==", "path": "sharpziplib/1.4.0", "hashPath": "sharpziplib.1.4.0.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-JfPLyigLthuE50yi6tMt7Amrenr/fA31t2CvJyhy/kQmfulIBAqo5T/YFUSRHtuYPXRSaUHygFeh6Qd933EoSw==", "path": "sixlabors.imagesharp/3.1.11", "hashPath": "sixlabors.imagesharp.3.1.11.nupkg.sha512"}, "SkiaSharp/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-wdfeBAQrEQCbJIRgAiargzP1Uy+0grZiG4CSgBnhAgcJTsPzlifIaO73JRdwIlT3TyBoeU9jEqzwFUhl4hTYnQ==", "path": "skiasharp/2.88.6", "hashPath": "skiasharp.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-Sko9LFxRXSjb3OGh5/RxrVRXxYo48tr5NKuuSy6jB85GrYt8WRqVY1iLOLwtjPiVAt4cp+pyD4i30azanS64dw==", "path": "skiasharp.nativeassets.macos/2.88.6", "hashPath": "skiasharp.nativeassets.macos.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-7TzFO0u/g2MpQsTty4fyCDdMcfcWI+aLswwfnYXr3gtNS6VLKdMXPMeKpJa3pJSLnUBN6wD0JjuCe8OoLBQ6cQ==", "path": "skiasharp.nativeassets.win32/2.88.6", "hashPath": "skiasharp.nativeassets.win32.2.88.6.nupkg.sha512"}, "System.Drawing.Common/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4ZM1wvLjz9nVVExsfPAaSl/qOvU+QNedJL5rQ+2Wbow+iGeyO0e7XN07707rMBgaffEeeLrCZBwC0oHUuvRdPw==", "path": "system.drawing.common/8.0.8", "hashPath": "system.drawing.common.8.0.8.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "TorchSharp/0.102.7": {"type": "package", "serviceable": true, "sha512": "sha512-P4bl1QcZxD+ZLrPjblUSv6MjRLlDWKa6loPZMMfekIubiandR2gdloL4cQZmfblxf5JzaUWwsbcoBqMu1uHZCw==", "path": "torchsharp/0.102.7", "hashPath": "torchsharp.0.102.7.nupkg.sha512"}, "TorchSharp-cuda-windows/0.102.7": {"type": "package", "serviceable": true, "sha512": "sha512-7bDTN3mIL0OPBq9fx+TUE4p2ta7Dx7ExrXpNKHMKEFzx1N1165bAmzAPORiQmWsQduDMFb+DsMHJ7AWfVL88wQ==", "path": "torchsharp-cuda-windows/0.102.7", "hashPath": "torchsharp-cuda-windows.0.102.7.nupkg.sha512"}}}