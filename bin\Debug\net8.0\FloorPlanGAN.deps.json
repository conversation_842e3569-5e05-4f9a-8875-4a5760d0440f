{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"FloorPlanGAN/1.0.0": {"dependencies": {"SixLabors.ImageSharp": "3.1.11", "System.Drawing.Common": "8.0.8", "TorchSharp": "0.101.5", "TorchSharp-cpu": "0.101.5"}, "runtime": {"FloorPlanGAN.dll": {}}}, "Google.Protobuf/3.21.9": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.21.9.0", "fileVersion": "3.21.9.0"}}}, "libtorch-cpu/*******": {"dependencies": {"libtorch-cpu-linux-x64": "*******", "libtorch-cpu-osx-x64": "*******", "libtorch-cpu-win-x64": "*******"}}, "libtorch-cpu-linux-x64/*******": {"runtimeTargets": {"runtimes/linux-x64/native/libbackend_with_compiler.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libc10.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libfbjni.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libgomp-a34b3233.so.1": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libjitbackend_test.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libnnapi_backend.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpytorch_jni.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libshm.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libtorch.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libtorch_cpu.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libtorch_global_deps.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libtorch_python.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libtorchbind_test.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "libtorch-cpu-osx-x64/*******": {"runtimeTargets": {"runtimes/osx-x64/native/libbackend_with_compiler.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libc10.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libfbjni.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libiomp5.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libjitbackend_test.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libpytorch_jni.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libshm.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libtorch.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libtorch_cpu.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libtorch_global_deps.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libtorch_python.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libtorchbind_test.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "libtorch-cpu-win-x64/*******": {"runtimeTargets": {"runtimes/win-x64/native/asmjit.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/c10.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/fbgemm.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/fbjni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libiomp5md.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.0.2023.118"}, "runtimes/win-x64/native/libiompstubs5md.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.0.2023.118"}, "runtimes/win-x64/native/pytorch_jni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/torch.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/torch_cpu.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/torch_global_deps.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/uv.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "SharpZipLib/1.4.0": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.ImageSharp/3.1.11": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "SkiaSharp/2.88.6": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.6", "SkiaSharp.NativeAssets.macOS": "2.88.6"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.Drawing.Common/8.0.8": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}}}, "System.Memory/4.5.5": {}, "TorchSharp/0.101.5": {"dependencies": {"Google.Protobuf": "3.21.9", "SharpZipLib": "1.4.0", "SkiaSharp": "2.88.6", "System.Memory": "4.5.5"}, "runtime": {"lib/net6.0/TorchSharp.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "runtimeTargets": {"runtimes/linux-x64/native/libLibTorchSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libLibTorchSharp.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/LibTorchSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "TorchSharp-cpu/0.101.5": {"dependencies": {"TorchSharp": "0.101.5", "libtorch-cpu": "*******"}}}}, "libraries": {"FloorPlanGAN/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Google.Protobuf/3.21.9": {"type": "package", "serviceable": true, "sha512": "sha512-OTpFujTgkmqMLbg3KT7F/iuKi1rg6s5FCS2M9XcVLDn40zL8wgXm37CY/F6MeOEXKjdcnXGCN/h7oyMkVydVsg==", "path": "google.protobuf/3.21.9", "hashPath": "google.protobuf.3.21.9.nupkg.sha512"}, "libtorch-cpu/*******": {"type": "package", "serviceable": true, "sha512": "sha512-SzckGCxIXSJQ2l2f7NaO3gFzNrgrxmYz248inHPZ3z7qMLqiKvTNCRVDNlMiKwLsI5x3wRzpsOwXrZT1d+SXgA==", "path": "libtorch-cpu/*******", "hashPath": "libtorch-cpu.*******.nupkg.sha512"}, "libtorch-cpu-linux-x64/*******": {"type": "package", "serviceable": true, "sha512": "sha512-gUdsUJMKdRaP0rkwYaaUhfP/Ue/hWM40qhWyhnkUJSk0Wc+BqQRoN/YlEm2PVyPvYTGQsAuaYR8v9K0uOxCWiA==", "path": "libtorch-cpu-linux-x64/*******", "hashPath": "libtorch-cpu-linux-x64.*******.nupkg.sha512"}, "libtorch-cpu-osx-x64/*******": {"type": "package", "serviceable": true, "sha512": "sha512-LpgbGL8hLhunAQHtDvfLlcbAPmKxxyHl9LqXMSKpe/LhJdfBJa1uJxIHsb00cVTef53nUnP1X3/I6MHhAwSZHw==", "path": "libtorch-cpu-osx-x64/*******", "hashPath": "libtorch-cpu-osx-x64.*******.nupkg.sha512"}, "libtorch-cpu-win-x64/*******": {"type": "package", "serviceable": true, "sha512": "sha512-zh9Ll+DefpXfMzaaEzHgtY3Q9iDi6NHTnETI0Jah5jGiPrzphTidsVxLC+njAeeThhFGRakx4CMwPqJFMvfw7A==", "path": "libtorch-cpu-win-x64/*******", "hashPath": "libtorch-cpu-win-x64.*******.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "SharpZipLib/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-CdkbBSPIpHD8xBlu+8kDJiqc1Tf9iV89BObnqcvEbwysXSj5h1MfaeLgeeaxPZmi7CTJO8FDofBBNxBW0Vml7A==", "path": "sharpziplib/1.4.0", "hashPath": "sharpziplib.1.4.0.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-JfPLyigLthuE50yi6tMt7Amrenr/fA31t2CvJyhy/kQmfulIBAqo5T/YFUSRHtuYPXRSaUHygFeh6Qd933EoSw==", "path": "sixlabors.imagesharp/3.1.11", "hashPath": "sixlabors.imagesharp.3.1.11.nupkg.sha512"}, "SkiaSharp/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-wdfeBAQrEQCbJIRgAiargzP1Uy+0grZiG4CSgBnhAgcJTsPzlifIaO73JRdwIlT3TyBoeU9jEqzwFUhl4hTYnQ==", "path": "skiasharp/2.88.6", "hashPath": "skiasharp.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-Sko9LFxRXSjb3OGh5/RxrVRXxYo48tr5NKuuSy6jB85GrYt8WRqVY1iLOLwtjPiVAt4cp+pyD4i30azanS64dw==", "path": "skiasharp.nativeassets.macos/2.88.6", "hashPath": "skiasharp.nativeassets.macos.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-7TzFO0u/g2MpQsTty4fyCDdMcfcWI+aLswwfnYXr3gtNS6VLKdMXPMeKpJa3pJSLnUBN6wD0JjuCe8OoLBQ6cQ==", "path": "skiasharp.nativeassets.win32/2.88.6", "hashPath": "skiasharp.nativeassets.win32.2.88.6.nupkg.sha512"}, "System.Drawing.Common/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4ZM1wvLjz9nVVExsfPAaSl/qOvU+QNedJL5rQ+2Wbow+iGeyO0e7XN07707rMBgaffEeeLrCZBwC0oHUuvRdPw==", "path": "system.drawing.common/8.0.8", "hashPath": "system.drawing.common.8.0.8.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "TorchSharp/0.101.5": {"type": "package", "serviceable": true, "sha512": "sha512-iKZyVgf3vJpVlU4rH94zr2K8mAhFslO2+htHCMoBRU089sjGnLarLvJT2KFDOOahsB0SZEsPFkScsAsj3iqj/A==", "path": "torchsharp/0.101.5", "hashPath": "torchsharp.0.101.5.nupkg.sha512"}, "TorchSharp-cpu/0.101.5": {"type": "package", "serviceable": true, "sha512": "sha512-p1+pYO1MyrC6LT5suhu/SpserikhcP+JM9ic49+iNsU+LWW9y+UNZcjr4Nldy4g6Uz63qhFImINfWBQ1PXUBjw==", "path": "torchsharp-cpu/0.101.5", "hashPath": "torchsharp-cpu.0.101.5.nupkg.sha512"}}}