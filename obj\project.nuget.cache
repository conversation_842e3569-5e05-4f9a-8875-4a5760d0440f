{"version": 2, "dgSpecHash": "k8/+wAJqMvc=", "success": true, "projectFilePath": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.21.9\\google.protobuf.3.21.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64\\*******\\libtorch-cuda-12.1-win-x64.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part1\\*******\\libtorch-cuda-12.1-win-x64-part1.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part10\\*******\\libtorch-cuda-12.1-win-x64-part10.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part2\\*******\\libtorch-cuda-12.1-win-x64-part2.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part3-fragment1\\*******\\libtorch-cuda-12.1-win-x64-part3-fragment1.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part3-primary\\*******\\libtorch-cuda-12.1-win-x64-part3-primary.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part4\\*******\\libtorch-cuda-12.1-win-x64-part4.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part5-fragment1\\*******\\libtorch-cuda-12.1-win-x64-part5-fragment1.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part5-primary\\*******\\libtorch-cuda-12.1-win-x64-part5-primary.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part6\\*******\\libtorch-cuda-12.1-win-x64-part6.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part7\\*******\\libtorch-cuda-12.1-win-x64-part7.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part8\\*******\\libtorch-cuda-12.1-win-x64-part8.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part9-fragment1\\*******\\libtorch-cuda-12.1-win-x64-part9-fragment1.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part9-fragment2\\*******\\libtorch-cuda-12.1-win-x64-part9-fragment2.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cuda-12.1-win-x64-part9-primary\\*******\\libtorch-cuda-12.1-win-x64-part9-primary.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\8.0.0\\microsoft.win32.systemevents.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpziplib\\1.4.0\\sharpziplib.1.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp\\3.1.11\\sixlabors.imagesharp.3.1.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\2.88.6\\skiasharp.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\2.88.6\\skiasharp.nativeassets.macos.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\2.88.6\\skiasharp.nativeassets.win32.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\8.0.8\\system.drawing.common.8.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\torchsharp\\0.102.7\\torchsharp.0.102.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\torchsharp-cuda-windows\\0.102.7\\torchsharp-cuda-windows.0.102.7.nupkg.sha512"], "logs": []}