{"version": 2, "dgSpecHash": "eLOI93Bg1bc=", "success": true, "projectFilePath": "D:\\MyWork\\05算法研究\\01代码\\01神经网络\\FloorPlanGAN\\FloorPlanGAN.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.21.9\\google.protobuf.3.21.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cpu\\*******\\libtorch-cpu.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cpu-linux-x64\\*******\\libtorch-cpu-linux-x64.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cpu-osx-x64\\*******\\libtorch-cpu-osx-x64.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libtorch-cpu-win-x64\\*******\\libtorch-cpu-win-x64.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\8.0.0\\microsoft.win32.systemevents.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpziplib\\1.4.0\\sharpziplib.1.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp\\3.1.11\\sixlabors.imagesharp.3.1.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\2.88.6\\skiasharp.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\2.88.6\\skiasharp.nativeassets.macos.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\2.88.6\\skiasharp.nativeassets.win32.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\8.0.8\\system.drawing.common.8.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\torchsharp\\0.101.5\\torchsharp.0.101.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\torchsharp-cpu\\0.101.5\\torchsharp-cpu.0.101.5.nupkg.sha512"], "logs": []}