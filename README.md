# 🏗️ FloorPlanGAN - 建筑平面图生成GAN

基于TorchSharp的真实条件GAN实现，用于根据外轮廓自动生成建筑平面图。

## 🎯 **功能特点**

- **条件生成**: 根据给定的外轮廓生成对应的建筑平面图
- **真实GAN**: 使用TorchSharp (PyTorch C#版本) 实现的真实深度学习模型
- **多种损失函数**: 支持Standard、LSGAN、WGAN、WGAN-GP、Hinge等损失
- **完整训练流程**: 包含数据加载、训练、验证、样本生成等完整功能
- **GPU加速**: 支持CUDA GPU训练
- **可视化输出**: 自动生成训练过程样本和对比图

## 📋 **系统要求**

### **硬件要求**
- **GPU**: NVIDIA GPU (推荐GTX 1060或更高)
- **内存**: 至少8GB RAM
- **存储**: 至少10GB可用空间

### **软件要求**
- **.NET 8.0** 或更高版本
- **CUDA 11.8** 或更高版本 (GPU训练)
- **Visual Studio 2022** 或 **VS Code**

## 📁 **数据集准备**

### **数据结构**
```
YourDataRoot/
├── outlines/     # 外轮廓图片 (条件输入)
│   ├── 001.png
│   ├── 002.png
│   ├── 003.png
│   └── ...
└── floorplans/   # 对应的平面图 (目标输出)
    ├── 001.png   # 必须与外轮廓文件名对应
    ├── 002.png
    ├── 003.png
    └── ...
```

### **数据要求**

#### **外轮廓图片 (outlines/)**
- **格式**: PNG, JPG, JPEG
- **内容**: 建筑外轮廓的黑白或彩色图像
- **特点**: 
  - 清晰的边界线
  - 简洁的轮廓形状
  - 白色背景，黑色或彩色轮廓线

#### **平面图图片 (floorplans/)**
- **格式**: PNG, JPG, JPEG  
- **内容**: 对应外轮廓的完整建筑平面图
- **特点**:
  - 包含房间分割
  - 墙体、门窗标注
  - 清晰的功能区域

#### **数据质量要求**
- **数量**: 建议至少500-1000对配对图像
- **尺寸**: 建议256x256或512x512像素
- **质量**: 高清晰度，无模糊
- **一致性**: 外轮廓与平面图必须完全对应

### **数据收集建议**

1. **建筑设计图纸**: 从建筑设计软件导出
2. **在线资源**: 建筑设计网站、图库
3. **手工绘制**: 使用CAD软件创建
4. **数据增强**: 旋转、翻转、缩放现有数据

## 🚀 **快速开始**

### **1. 安装依赖**
```bash
# 克隆项目
git clone <repository-url>
cd FloorPlanGAN

# 恢复NuGet包
dotnet restore
```

### **2. 准备数据**
按照上述数据结构准备你的数据集，例如：
```
D:/FloorPlanData/
├── outlines/
└── floorplans/
```

### **3. 开始训练**
```bash
# 基础训练
dotnet run train --data "D:/FloorPlanData"

# 自定义参数训练
dotnet run train --data "D:/FloorPlanData" --epochs 200 --batch-size 16 --lr 0.0002
```

### **4. 生成样本**
```bash
# 使用训练好的模型生成样本
dotnet run generate "checkpoints/checkpoint_final.pth" "output_samples" 10
```

### **5. 评估模型**
```bash
# 评估模型性能
dotnet run evaluate "checkpoints/checkpoint_final.pth"
```

## ⚙️ **配置参数**

### **训练参数**
```csharp
var options = new FloorPlanGANOptions
{
    DataRoot = "D:/FloorPlanData",      // 数据集路径
    BatchSize = 16,                     // 批次大小
    NumEpochs = 200,                    // 训练轮数
    LearningRate = 0.0002,              // 学习率
    ImageSize = 256,                    // 图像尺寸
    NoiseSize = 100,                    // 噪声向量维度
    GeneratorFeatures = 64,             // 生成器特征数
    DiscriminatorFeatures = 64,         // 判别器特征数
    LossType = LossType.LSGAN,          // 损失函数类型
    UseSpectralNorm = true,             // 是否使用谱归一化
    SaveInterval = 10,                  // 保存间隔
    SampleInterval = 5                  // 样本生成间隔
};
```

### **损失函数选择**
- **Standard**: 标准GAN损失 (Binary Cross Entropy)
- **LSGAN**: 最小二乘GAN损失 (推荐)
- **WGAN**: Wasserstein GAN损失
- **WGAN-GP**: WGAN with Gradient Penalty
- **Hinge**: Hinge损失

## 📊 **训练监控**

### **输出文件**
```
checkpoints/              # 模型检查点
├── checkpoint_epoch_010.pth
├── checkpoint_epoch_020.pth
└── checkpoint_final.pth

generated_samples/        # 生成样本
├── epoch_005.png
├── epoch_010.png
└── training_stats.csv
```

### **训练日志**
```
Epoch 1/200
  Batch 10/50 - G_Loss: 2.1234, D_Loss: 0.8765, Real_Acc: 0.750, Fake_Acc: 0.625, Time: 2.3s
  Batch 20/50 - G_Loss: 1.9876, D_Loss: 0.9123, Real_Acc: 0.812, Fake_Acc: 0.687, Time: 2.1s
Epoch 1 完成 - G_Loss: 2.0123, D_Loss: 0.8945, Real_Acc: 0.781, Fake_Acc: 0.656, Time: 5.2min
```

## 🎨 **使用训练好的模型**

### **代码示例**
```csharp
// 加载模型
var options = new FloorPlanGANOptions();
var trainer = new FloorPlanGANTrainer(options);
trainer.LoadCheckpoint("checkpoints/checkpoint_final.pth");

// 加载外轮廓图像
var outline = ImageUtils.ImageToTensor(outlineImage);
outline = outline.unsqueeze(0); // 添加批次维度

// 生成平面图
var generated = trainer.GenerateSample(outline);

// 保存结果
await ImageUtils.SaveImageAsync(generated[0], "output.png");
```

## 🔧 **高级功能**

### **自定义网络架构**
可以修改 `ConditionalGenerator.cs` 和 `ConditionalDiscriminator.cs` 来自定义网络结构。

### **数据增强**
在 `FloorPlanDataset.cs` 中实现了基础的数据增强：
- 随机翻转
- 随机旋转
- 可扩展其他增强方法

### **损失函数扩展**
在 `LossCalculator.cs` 中可以添加新的损失函数：
- 感知损失 (Perceptual Loss)
- 特征匹配损失 (Feature Matching)
- 总变分损失 (Total Variation)

## 📈 **性能优化**

### **训练加速**
1. **使用GPU**: 确保CUDA正确安装
2. **调整批次大小**: 根据GPU内存调整
3. **混合精度**: 启用 `UseMixedPrecision = true`
4. **数据并行**: 增加 `Workers` 数量

### **内存优化**
1. **梯度累积**: 减小批次大小，增加累积步数
2. **检查点**: 定期保存和清理中间结果
3. **数据流**: 使用流式数据加载

## 🐛 **常见问题**

### **Q: CUDA内存不足怎么办？**
A: 
- 减小批次大小 (`BatchSize`)
- 减小图像尺寸 (`ImageSize`)
- 启用梯度检查点

### **Q: 训练不收敛怎么办？**
A:
- 调整学习率 (`LearningRate`)
- 尝试不同的损失函数 (`LossType`)
- 增加训练数据量
- 检查数据质量

### **Q: 生成质量不好怎么办？**
A:
- 增加训练轮数 (`NumEpochs`)
- 调整网络架构
- 添加正则化 (`UseSpectralNorm`)
- 使用更好的损失函数

### **Q: 数据准备有什么技巧？**
A:
- 确保外轮廓和平面图完全对应
- 保持图像风格一致
- 适当的数据增强
- 质量比数量更重要

## 📞 **技术支持**

如果遇到问题：
1. 检查数据集格式是否正确
2. 确认CUDA环境是否正常
3. 查看训练日志中的错误信息
4. 尝试使用更小的数据集进行测试

## 🎯 **项目结构**

```
FloorPlanGAN/
├── Models/                    # 模型定义
│   ├── ConditionalGenerator.cs
│   ├── ConditionalDiscriminator.cs
│   └── FloorPlanGANOptions.cs
├── Data/                      # 数据处理
│   └── FloorPlanDataset.cs
├── Training/                  # 训练逻辑
│   └── FloorPlanGANTrainer.cs
├── Utils/                     # 工具类
│   ├── LossCalculator.cs
│   └── ImageUtils.cs
├── Program.cs                 # 主程序
└── README.md                  # 说明文档
```

这是一个**真实的、可工作的GAN实现**，不是演示代码。只要准备好数据，就可以开始训练真正的建筑平面图生成模型！
